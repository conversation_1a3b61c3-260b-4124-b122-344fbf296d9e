"""
Background Document Processing Service for RepoSense AI

Handles asynchronous document scanning, metadata extraction, and database updates.
Provides high-performance document processing without blocking web requests.
"""

import os
import re
import time
import threading
import logging
import hashlib
from pathlib import Path
from datetime import datetime
from typing import List, Optional, Set, Dict
from queue import Queue, Empty
from dataclasses import dataclass

from document_database import DocumentDatabase, DocumentRecord
from metadata_extractor import MetadataExtractor


@dataclass
class ProcessingTask:
    """Task for background document processing"""
    filepath: str
    priority: int = 0  # Higher numbers = higher priority
    
    def __lt__(self, other):
        return self.priority > other.priority  # Reverse for max-heap behavior


class DocumentProcessor:
    """Background service for processing documents"""

    def __init__(self, output_dir: str = "/app/data/output", db_path: str = "/app/data/documents.db", config_manager=None):
        self.output_dir = Path(output_dir)
        self.db = DocumentDatabase(db_path)
        self.config_manager = config_manager
        self.metadata_extractor = MetadataExtractor()  # No Ollama client in DocumentProcessor
        self.logger = logging.getLogger(__name__)

        # Processing queue and control
        self.task_queue: Queue[ProcessingTask] = Queue()
        self.processing_thread: Optional[threading.Thread] = None
        self.running = False
        self.processed_files: Set[str] = set()

        # File change tracking
        self.file_checksums: Dict[str, str] = {}
        self.file_mtimes: Dict[str, float] = {}

        # Repository name to ID mapping cache
        self._repo_name_to_id_cache: Dict[str, str] = {}
        self._cache_timestamp = 0.0
        self._cache_ttl = 300.0  # 5 minutes

        # Performance tracking
        self.stats: Dict[str, float] = {
            'processed_count': 0,
            'error_count': 0,
            'last_scan_time': 0.0,
            'processing_time_total': 0.0
        }
    
    def start(self):
        """Start background processing"""
        if self.running:
            return

        # Load change tracking data
        self._load_change_tracking_data()

        self.running = True
        self.processing_thread = threading.Thread(target=self._processing_loop, daemon=True)
        self.processing_thread.start()
        self.logger.info("Document processor started")
    
    def stop(self):
        """Stop background processing"""
        self.running = False
        if self.processing_thread:
            self.processing_thread.join(timeout=5.0)

        # Save change tracking data
        self._save_change_tracking_data()

        self.logger.info("Document processor stopped")
    
    def scan_and_queue_documents(self, force_rescan: bool = False):
        """Scan for documents and queue processing tasks"""
        start_time = time.time()
        queued_count = 0
        
        try:
            repositories_dir = self.output_dir / "repositories"
            if not repositories_dir.exists():
                return
            
            for repo_dir in repositories_dir.iterdir():
                if not repo_dir.is_dir():
                    continue
                
                docs_dir = repo_dir / "docs"
                if not docs_dir.exists():
                    continue
                
                for doc_file in docs_dir.glob("*.md"):
                    if not doc_file.is_file():
                        continue
                    
                    filepath = str(doc_file)
                    file_mtime = doc_file.stat().st_mtime
                    
                    # Check if file needs processing
                    if self._needs_processing(filepath, file_mtime, force_rescan):
                        # Higher priority for newer files
                        priority = int(file_mtime)
                        task = ProcessingTask(filepath, priority)
                        self.task_queue.put(task)
                        queued_count += 1
            
            scan_time = time.time() - start_time
            self.stats['last_scan_time'] = scan_time
            
            if queued_count > 0:
                self.logger.info(f"Queued {queued_count} documents for processing (scan took {scan_time:.2f}s)")
        
        except Exception as e:
            self.logger.error(f"Error scanning documents: {e}")
    
    def _needs_processing(self, filepath: str, file_mtime: float, force_rescan: bool) -> bool:
        """Enhanced change detection with multiple validation methods"""
        if force_rescan:
            return True

        # Check if already processed recently
        if filepath in self.processed_files:
            return False

        # Check database for existing record
        doc_id = self._generate_doc_id(filepath)
        existing_doc = self.db.get_document_by_id(doc_id)

        if not existing_doc:
            return True  # New document

        # Multi-level change detection

        # 1. Check modification time
        if existing_doc.file_modified_time and file_mtime <= existing_doc.file_modified_time:
            # File hasn't been modified, but let's double-check with content hash
            if self._has_content_changed(filepath):
                return True  # Content changed despite same mtime

            self.processed_files.add(filepath)  # Mark as processed
            return False

        # 2. File was modified based on mtime
        return True

    def _has_content_changed(self, filepath: str) -> bool:
        """Check if file content has changed using checksums"""
        try:
            # Calculate current file checksum
            current_checksum = self._calculate_file_checksum(filepath)

            # Compare with stored checksum
            stored_checksum = self.file_checksums.get(filepath)
            if stored_checksum and current_checksum == stored_checksum:
                return False  # Content unchanged

            # Update stored checksum
            self.file_checksums[filepath] = current_checksum
            return True  # Content changed or first time

        except Exception as e:
            self.logger.warning(f"Error checking content change for {filepath}: {e}")
            return True  # Assume changed on error

    def _calculate_file_checksum(self, filepath: str) -> str:
        """Calculate MD5 checksum of file content"""
        hash_md5 = hashlib.md5()
        try:
            with open(filepath, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    def _processing_loop(self):
        """Main processing loop"""
        while self.running:
            try:
                # Get task from queue (with timeout)
                task = self.task_queue.get(timeout=1.0)
                self._process_document(task.filepath)
                self.task_queue.task_done()
                
            except Empty:
                continue  # No tasks, keep looping
            except Exception as e:
                self.logger.error(f"Error in processing loop: {e}")
                self.stats['error_count'] += 1
    
    def _process_document(self, filepath: str):
        """Process a single document"""
        start_time = time.time()
        
        try:
            doc_file = Path(filepath)
            if not doc_file.exists():
                self.logger.warning(f"Document file not found: {filepath}")
                return
            
            # Parse document
            doc_record = self._parse_document_file(doc_file)
            if not doc_record:
                self.logger.warning(f"Failed to parse document: {filepath}")
                return
            
            # Update database
            if self.db.upsert_document(doc_record):
                self.processed_files.add(filepath)
                self.stats['processed_count'] += 1
                
                processing_time = time.time() - start_time
                self.stats['processing_time_total'] += processing_time
                
                self.logger.debug(f"Processed document {doc_record.id} in {processing_time:.3f}s")
            else:
                self.stats['error_count'] += 1
        
        except Exception as e:
            self.logger.error(f"Error processing document {filepath}: {e}")
            self.stats['error_count'] += 1
    
    def _parse_document_file(self, doc_file: Path) -> Optional[DocumentRecord]:
        """Parse document file and extract metadata"""
        try:
            # Parse filename to extract revision and date
            filename_match = re.match(r'revision_(\d+)_(\d{4}-\d{2}-\d{2})\.md', doc_file.name)
            if not filename_match:
                # Try simpler pattern without date
                filename_match = re.match(r'revision_(\d+)\.md', doc_file.name)
                if not filename_match:
                    return None
                revision = int(filename_match.group(1))
                date_str = None
            else:
                revision = int(filename_match.group(1))
                date_str = filename_match.group(2)

            # Get repository directory name and map to actual repository ID
            repo_dir_name = doc_file.parent.parent.name
            repo_id = self._get_repository_id_from_name(repo_dir_name)
            repo_name = self._get_repository_name_from_dir(repo_dir_name)

            # Read file content
            with open(doc_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # SINGLE SOURCE OF TRUTH: Always get data from repository backend first
            # Only use markdown content as absolute fallback if repository is unavailable
            commit_info = self._get_commit_info_from_repository(repo_id, revision)

            if commit_info:
                # Use repository as single source of truth
                author = commit_info.author or "Unknown"
                commit_message = commit_info.message or "No commit message available"
                changed_paths = commit_info.changed_paths

                # If no commit message from repository, try to use AI-generated summary
                if not commit_info.message or commit_info.message.strip() == "":
                    ai_summary = self.metadata_extractor.extract_ai_summary(content)
                    if ai_summary:
                        commit_message = f"{ai_summary} (AI-generated summary)"
                        self.logger.debug(f"Using AI-generated summary as commit message for revision {revision}")

                # Use repository date if available
                if commit_info.date:
                    try:
                        if isinstance(commit_info.date, str):
                            doc_date = datetime.fromisoformat(commit_info.date.replace('Z', '+00:00'))
                        else:
                            doc_date = commit_info.date
                    except (ValueError, TypeError):
                        # Fallback to filename date parsing below
                        pass
                else:
                    # Fallback to filename date parsing below
                    pass
            else:
                # FALLBACK ONLY: Repository unavailable, extract from markdown content
                self.logger.warning(f"Repository unavailable for {repo_id} revision {revision}, using markdown content as fallback")
                author = self.metadata_extractor.extract_field(content, "Author") or "Unknown"
                commit_message = self.metadata_extractor.extract_field(content, "Message") or "No commit message available"
                changed_paths = None
                date_field = self.metadata_extractor.extract_field(content, "Date")

                # If no commit message from markdown, try to use AI-generated summary
                if commit_message == "No commit message available":
                    ai_summary = self.metadata_extractor.extract_ai_summary(content)
                    if ai_summary:
                        commit_message = f"{ai_summary} (AI-generated summary)"
                        self.logger.debug(f"Using AI-generated summary as commit message for revision {revision} (fallback mode)")

                # Try to parse date from markdown content
                if date_field:
                    try:
                        doc_date = datetime.fromisoformat(date_field.replace('Z', '+00:00'))
                    except ValueError:
                        pass  # Will use filename date parsing below

            # Final fallback for date: use filename date or current time
            if 'doc_date' not in locals():
                if date_str:
                    try:
                        doc_date = datetime.strptime(date_str, '%Y-%m-%d')
                    except ValueError:
                        doc_date = datetime.now()
                else:
                    doc_date = datetime.now()
            
            # Extract LLM analysis metadata using centralized extractor
            metadata = self.metadata_extractor.extract_all_metadata(content)
            code_review_recommended = metadata.get('code_review_recommended')
            code_review_priority = metadata.get('code_review_priority')
            documentation_impact = metadata.get('documentation_impact')
            risk_level = metadata.get('risk_level')
            
            # File metadata
            file_stat = doc_file.stat()
            doc_id = self._generate_doc_id(str(doc_file))
            
            return DocumentRecord(
                id=doc_id,
                repository_id=repo_id,
                repository_name=repo_name,
                revision=revision,
                date=doc_date,
                filename=doc_file.name,
                filepath=str(doc_file),
                size=file_stat.st_size,
                author=author,
                commit_message=commit_message,
                changed_paths=changed_paths,  # Now available from repository backend
                code_review_recommended=code_review_recommended,
                code_review_priority=code_review_priority,
                documentation_impact=documentation_impact,
                risk_level=risk_level,
                file_modified_time=file_stat.st_mtime,
                processed_time=datetime.now()
            )
        
        except Exception as e:
            self.logger.error(f"Error parsing document file {doc_file}: {e}")
            return None
    
    def _get_repository_id_from_name(self, repo_dir_name: str) -> str:
        """Map repository directory name to actual repository UUID"""
        try:
            # Check cache first
            current_time = time.time()
            if (current_time - self._cache_timestamp < self._cache_ttl and
                repo_dir_name in self._repo_name_to_id_cache):
                return self._repo_name_to_id_cache[repo_dir_name]

            # Refresh cache if needed
            if current_time - self._cache_timestamp >= self._cache_ttl:
                self._refresh_repository_cache()

            # Return mapped ID or fallback to directory name
            return self._repo_name_to_id_cache.get(repo_dir_name, repo_dir_name)

        except Exception as e:
            self.logger.warning(f"Error mapping repository name {repo_dir_name} to ID: {e}")
            return repo_dir_name  # Fallback to directory name

    def _get_repository_name_from_dir(self, repo_dir_name: str) -> str:
        """Get the actual repository name (not UUID) from directory name"""
        try:
            # Check cache first
            current_time = time.time()
            if current_time - self._cache_timestamp >= self._cache_ttl:
                self._refresh_repository_cache()

            # Check if the directory name is a UUID (repository ID)
            # If so, look up the actual repository name from config
            if self._is_uuid(repo_dir_name):
                # This is a repository ID, find the corresponding name
                for name, repo_id in self._repo_name_to_id_cache.items():
                    if repo_id == repo_dir_name:
                        self.logger.debug(f"Mapped UUID directory '{repo_dir_name}' to repository name '{name}'")
                        return name

                # If not found in cache, try to load from config
                if self.config_manager:
                    config = self.config_manager.load_config()
                    for repo in config.repositories:
                        if repo.id == repo_dir_name:
                            self.logger.debug(f"Found repository name '{repo.name}' for UUID '{repo_dir_name}'")
                            return repo.name

                # If still not found, log warning and return the UUID
                self.logger.warning(f"Could not find repository name for UUID directory '{repo_dir_name}', using UUID as name")
                return repo_dir_name
            else:
                # The directory name IS the repository name
                # This is the human-readable name like "reposense_ai" or "visionApi"
                return repo_dir_name

        except Exception as e:
            self.logger.warning(f"Error getting repository name for {repo_dir_name}: {e}")
            return repo_dir_name  # Fallback to directory name

    def _is_uuid(self, value: str) -> bool:
        """Check if a string is a valid UUID"""
        try:
            import uuid
            uuid.UUID(value)
            return True
        except (ValueError, AttributeError):
            return False

    def _refresh_repository_cache(self):
        """Refresh the repository name to ID mapping cache"""
        try:
            self._repo_name_to_id_cache.clear()

            if self.config_manager:
                config = self.config_manager.load_config()
                for repo in config.repositories:
                    # Map repository name to UUID
                    self._repo_name_to_id_cache[repo.name] = repo.id
                    self.logger.debug(f"Mapped repository '{repo.name}' to ID '{repo.id}'")

            self._cache_timestamp = time.time()
            self.logger.debug(f"Repository cache refreshed with {len(self._repo_name_to_id_cache)} repositories")

        except Exception as e:
            self.logger.error(f"Error refreshing repository cache: {e}")

    def _generate_doc_id(self, filepath: str) -> str:
        """Generate consistent document ID from filepath"""
        # Extract repo and revision from path
        path_parts = Path(filepath).parts
        if len(path_parts) >= 3:
            repo_dir_name = path_parts[-3]  # repositories/repo_dir_name/docs/file.md
            repo_id = self._get_repository_id_from_name(repo_dir_name)
            filename = path_parts[-1]
            return f"{repo_id}_{filename.replace('.md', '')}"
        return Path(filepath).stem
    
    def _extract_field(self, content: str, field_name: str) -> Optional[str]:
        """Extract field value from document content"""
        pattern = rf'\*\*{re.escape(field_name)}:\*\*\s*(.+?)(?:\n|$)'
        match = re.search(pattern, content)
        return match.group(1).strip() if match else None
    
    def _extract_code_review_recommendation(self, content: str) -> Optional[bool]:
        """Extract code review recommendation from LLM analysis"""
        try:
            review_section = self._extract_section(content, "Code Review Recommendation")
            if not review_section:
                return None

            review_lower = review_section.lower()
            if ("not required" in review_lower or "no review" in review_lower or "skip review" in review_lower or
                "does not require" in review_lower or "should not require" in review_lower or
                "not be subject to" in review_lower or "not necessary" in review_lower):
                return False
            elif ("recommended" in review_lower or "should be reviewed" in review_lower or
                  "should be code reviewed" in review_lower or "requires review" in review_lower or
                  "should be considered" in review_lower or "consider" in review_lower or
                  "priority" in review_lower):
                return True

            return None
        except Exception:
            return None
    
    def _extract_code_review_priority(self, content: str) -> Optional[str]:
        """Extract code review priority from LLM analysis"""
        try:
            review_section = self._extract_section(content, "Code Review Recommendation")
            if not review_section:
                return None

            review_lower = review_section.lower()

            # Explicit priority mentions
            if "high priority" in review_lower or "urgent" in review_lower or "critical" in review_lower:
                return "HIGH"
            elif "medium priority" in review_lower or "moderate" in review_lower:
                return "MEDIUM"
            elif "low priority" in review_lower or "optional" in review_lower:
                return "LOW"

            # Infer priority from context (check negative indicators first)
            if ("may not require" in review_lower or "simple" in review_lower or
                "straightforward" in review_lower or "minimal" in review_lower or
                "quick glance" in review_lower or "due to its simplicity" in review_lower):
                return "LOW"
            elif ("extensive review" in review_lower or "thorough" in review_lower or
                  "careful" in review_lower or "detailed" in review_lower):
                return "HIGH"
            else:
                return "MEDIUM"  # Default to medium if review is recommended but no clear priority

        except Exception:
            return None
    
    def _extract_documentation_impact(self, content: str) -> Optional[bool]:
        """Extract documentation impact from LLM analysis"""
        try:
            doc_section = self._extract_section(content, "Documentation Impact")
            if not doc_section:
                return None
            
            doc_lower = doc_section.lower()
            if ("not required" in doc_lower or "no updates" in doc_lower or "no impact" in doc_lower or
                "no documentation updates" in doc_lower):
                return False
            elif "required" in doc_lower or "should be updated" in doc_lower or "needs update" in doc_lower:
                return True
            
            return None
        except Exception:
            return None
    
    def _extract_risk_level(self, content: str) -> Optional[str]:
        """Extract risk level from LLM analysis"""
        try:
            sections = ["Code Review Recommendation", "Impact Assessment", "Summary", "Recommendations"]

            for section_name in sections:
                section = self._extract_section(content, section_name)
                if section:
                    section_lower = section.lower()

                    # Explicit risk level mentions
                    if "high risk" in section_lower or "risk level: high" in section_lower:
                        return "HIGH"
                    elif "medium risk" in section_lower or "risk level: medium" in section_lower:
                        return "MEDIUM"
                    elif "low risk" in section_lower or "risk level: low" in section_lower:
                        return "LOW"

                    # Infer risk from context
                    if ("security risks" in section_lower or "complex technical dependencies" in section_lower or
                        "breaking changes" in section_lower or "critical" in section_lower):
                        # Check if it says "no security risks" or "does not introduce"
                        if ("no security risks" in section_lower or "does not introduce" in section_lower or
                            "no additional" in section_lower):
                            return "LOW"
                        else:
                            return "HIGH"
                    elif ("minimal impact" in section_lower or "straightforward" in section_lower or
                          "simple" in section_lower or "no additional follow-up" in section_lower):
                        return "LOW"

            return "MEDIUM"  # Default to medium if no clear indicators
        except Exception:
            return None
    
    def _extract_section(self, content: str, section_name: str) -> Optional[str]:
        """Extract a specific section from the document content"""
        try:
            pattern = rf'##\s*{re.escape(section_name)}\s*\n(.*?)(?=\n##|\Z)'
            match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
            return match.group(1).strip() if match else None
        except Exception:
            return None

    def _extract_ai_summary(self, content: str) -> Optional[str]:
        """Extract AI-generated summary from document content to use as commit message"""
        try:
            # Try to extract the Summary section
            summary = self._extract_section(content, "Summary")
            if summary:
                # Clean up the summary - take first sentence or first line if it's concise
                lines = summary.strip().split('\n')
                first_line = lines[0].strip()

                # If first line is a complete sentence and not too long, use it
                if first_line and len(first_line) <= 100 and (first_line.endswith('.') or len(lines) == 1):
                    return first_line.rstrip('.')

                # Otherwise, try to find a concise summary sentence
                for line in lines:
                    line = line.strip()
                    if line and len(line) <= 100 and not line.startswith('*') and not line.startswith('-'):
                        return line.rstrip('.')

                # Fallback: use first 80 characters of summary
                if len(first_line) > 0:
                    return (first_line[:80] + '...') if len(first_line) > 80 else first_line.rstrip('.')

            return None
        except Exception as e:
            self.logger.debug(f"Error extracting AI summary: {e}")
            return None
    
    def get_stats(self) -> dict:
        """Get processing statistics"""
        avg_processing_time = 0.0
        if self.stats['processed_count'] > 0:
            avg_processing_time = self.stats['processing_time_total'] / self.stats['processed_count']
        
        return {
            **self.stats,
            'queue_size': self.task_queue.qsize(),
            'running': self.running,
            'avg_processing_time': avg_processing_time,
            'tracked_files': len(self.file_checksums)
        }

    def _load_change_tracking_data(self):
        """Load change tracking data from disk"""
        try:
            tracking_file = Path(self.output_dir.parent) / "change_tracking.json"
            if tracking_file.exists():
                import json
                with open(tracking_file, 'r') as f:
                    data = json.load(f)
                    self.file_checksums = data.get('checksums', {})
                    self.file_mtimes = data.get('mtimes', {})
                    self.logger.info(f"Loaded change tracking data for {len(self.file_checksums)} files")
        except Exception as e:
            self.logger.warning(f"Error loading change tracking data: {e}")
            self.file_checksums = {}
            self.file_mtimes = {}

    def _save_change_tracking_data(self):
        """Save change tracking data to disk"""
        try:
            tracking_file = Path(self.output_dir.parent) / "change_tracking.json"
            import json
            data = {
                'checksums': self.file_checksums,
                'mtimes': self.file_mtimes,
                'last_save': time.time()
            }
            with open(tracking_file, 'w') as f:
                json.dump(data, f, indent=2)
            self.logger.debug(f"Saved change tracking data for {len(self.file_checksums)} files")
        except Exception as e:
            self.logger.error(f"Error saving change tracking data: {e}")

    def _get_commit_info_from_repository(self, repo_id: str, revision: int):
        """Get commit information from repository backend"""
        try:
            # Import here to avoid circular imports
            from repository_backends import get_backend_manager
            from config_manager import ConfigManager

            # Get repository configuration
            config_manager = ConfigManager()
            config = config_manager.load_config()

            # Find the repository config by ID
            repo_config = None
            for repo in config.repositories:
                if repo.id == repo_id:
                    repo_config = repo
                    break

            if not repo_config:
                self.logger.debug(f"Repository config not found for ID: {repo_id}")
                return None

            # Get backend for this repository
            backend_manager = get_backend_manager()
            backend = backend_manager.get_backend_for_repository(repo_config, config)

            if not backend:
                self.logger.debug(f"No backend available for repository: {repo_config.name}")
                return None

            # Get commit info
            commit_info = backend.get_commit_info(repo_config, str(revision))
            if commit_info:
                self.logger.debug(f"Retrieved commit info for revision {revision} from repository {repo_config.name}")
                return commit_info
            else:
                self.logger.debug(f"No commit info found for revision {revision} in repository {repo_config.name}")
                return None

        except Exception as e:
            self.logger.warning(f"Error getting commit info from repository for revision {revision}: {e}")
            return None
