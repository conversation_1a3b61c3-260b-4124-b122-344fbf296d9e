#!/usr/bin/env python3
"""
Metadata Extractor Service
Centralized service for extracting metadata from documentation content.
Consolidates logic previously duplicated across HistoricalScanner, DocumentProcessor, and DocumentService.
"""

import logging
import re
import json
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path


class MetadataExtractor:
    """Centralized metadata extraction service with hybrid heuristic + LLM approach"""
    
    def __init__(self, ollama_client=None):
        """
        Initialize metadata extractor
        
        Args:
            ollama_client: Optional Ollama client for LLM fallback
        """
        self.logger = logging.getLogger(__name__)
        self.ollama_client = ollama_client
    
    def extract_all_metadata(self, documentation: str) -> Dict[str, Any]:
        """Extract all metadata from generated documentation using hybrid approach"""
        metadata: Dict[str, Any] = {}
        
        try:
            # First try heuristic extraction (fast)
            code_review_rec = self.extract_code_review_recommendation(documentation)
            if code_review_rec is not None:
                metadata['code_review_recommended'] = code_review_rec
                
                # Extract priority if review is recommended
                if code_review_rec:
                    priority = self.extract_code_review_priority(documentation)
                    if priority:
                        metadata['code_review_priority'] = priority
            
            # Extract documentation impact using robust section-based approach
            doc_impact = self.extract_documentation_impact(documentation)
            if doc_impact is not None:
                metadata['documentation_impact'] = doc_impact
            
            # Extract risk level using robust section-based approach
            risk_level = self.extract_risk_level(documentation)
            if risk_level:
                metadata['risk_level'] = risk_level
            
            # Check for missing fields that need LLM fallback
            expected_fields = ['code_review_recommended', 'documentation_impact', 'risk_level']
            missing_fields = [field for field in expected_fields if field not in metadata]
            
            if missing_fields and self.ollama_client:
                self.logger.info(f"Heuristic extraction incomplete for {missing_fields}, using LLM fallback")
                llm_metadata = self._extract_metadata_with_llm(documentation)
                
                # Fill in missing values with LLM results
                for field in missing_fields:
                    if llm_metadata.get(field) is not None:
                        metadata[field] = llm_metadata[field]
                        self.logger.debug(f"LLM provided {field}: {llm_metadata[field]}")
        
        except Exception as e:
            self.logger.error(f"Error extracting document metadata: {e}")
        
        return metadata
    
    def extract_ai_summary(self, content: str) -> Optional[str]:
        """Extract AI-generated summary from document content to use as commit message"""
        try:
            # Look for the Summary section in the documentation
            summary_content = self.extract_section(content, "Summary")
            
            if summary_content:
                # Clean up the summary - take first sentence or first line if it's concise
                lines = summary_content.split('\n')
                first_line = lines[0].strip()
                
                # If first line is a complete sentence and not too long, use it
                if first_line and len(first_line) <= 100 and (first_line.endswith('.') or len(lines) == 1):
                    return first_line.rstrip('.')
                
                # Otherwise, try to find a concise summary sentence
                for line in lines:
                    line = line.strip()
                    if line and len(line) <= 100 and not line.startswith('*') and not line.startswith('-'):
                        return line.rstrip('.')
                
                # Fallback: use first 80 characters of summary
                if len(first_line) > 0:
                    return (first_line[:80] + '...') if len(first_line) > 80 else first_line.rstrip('.')
            
            return None
        except Exception as e:
            self.logger.debug(f"Error extracting AI summary: {e}")
            return None
    
    def extract_section(self, content: str, section_name: str) -> Optional[str]:
        """Extract a specific section from the document content"""
        try:
            pattern = rf'##\s*{re.escape(section_name)}\s*\n(.*?)(?=\n##|\Z)'
            match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
            return match.group(1).strip() if match else None
        except Exception:
            return None
    
    def extract_field(self, content: str, field_name: str) -> Optional[str]:
        """Extract field value from document content (for markdown metadata)"""
        pattern = rf'\*\*{re.escape(field_name)}:\*\*\s*(.+?)(?:\n|$)'
        match = re.search(pattern, content)
        return match.group(1).strip() if match else None
    
    def parse_date_with_fallbacks(self, date_input: Any, filename_date: Optional[str] = None) -> datetime:
        """Parse date with multiple fallback strategies"""
        # Try repository date first
        if date_input:
            try:
                if isinstance(date_input, str):
                    return datetime.fromisoformat(date_input.replace('Z', '+00:00'))
                elif isinstance(date_input, datetime):
                    return date_input
            except (ValueError, TypeError):
                pass
        
        # Try filename date
        if filename_date:
            try:
                return datetime.strptime(filename_date, '%Y-%m-%d')
            except ValueError:
                pass
        
        # Final fallback
        return datetime.now()
    
    def generate_document_id(self, repository_id: str, revision: int) -> str:
        """Generate consistent document ID"""
        return f"{repository_id}_{revision}"
    
    def extract_code_review_recommendation(self, content: str) -> Optional[bool]:
        """Extract code review recommendation from LLM analysis with LLM fallback"""
        try:
            # First try heuristic extraction
            review_section = self.extract_section(content, "Code Review Recommendation")
            if review_section:
                review_lower = review_section.lower()
                if ("not required" in review_lower or "no review" in review_lower or "skip review" in review_lower or
                    "does not require" in review_lower or "should not require" in review_lower or
                    "not be subject to" in review_lower or "not necessary" in review_lower):
                    return False
                elif ("recommended" in review_lower or "should be reviewed" in review_lower or
                      "should be code reviewed" in review_lower or "requires review" in review_lower or
                      "should be considered" in review_lower or "consider" in review_lower or
                      "priority" in review_lower):
                    return True
            
            # If heuristic failed and we have LLM client, try LLM fallback
            if self.ollama_client:
                self.logger.debug("Heuristic code review extraction failed, trying LLM fallback")
                llm_result = self._extract_metadata_with_llm(content)
                return llm_result.get('code_review_recommended')
            
            return None
        except Exception:
            return None
    
    def extract_code_review_priority(self, content: str) -> Optional[str]:
        """Extract code review priority from LLM analysis with LLM fallback"""
        try:
            # First try heuristic extraction
            review_section = self.extract_section(content, "Code Review Recommendation")
            if review_section:
                review_lower = review_section.lower()
                if "high priority" in review_lower or "critical" in review_lower or "urgent" in review_lower:
                    return "HIGH"
                elif "medium priority" in review_lower or "moderate" in review_lower:
                    return "MEDIUM"
                elif "low priority" in review_lower or "minor" in review_lower:
                    return "LOW"
            
            # If heuristic failed and we have LLM client, try LLM fallback
            if self.ollama_client:
                self.logger.debug("Heuristic priority extraction failed, trying LLM fallback")
                llm_result = self._extract_metadata_with_llm(content)
                return llm_result.get('code_review_priority')
            
            return None
        except Exception as e:
            self.logger.debug(f"Error extracting code review priority: {e}")
            return None
    
    def extract_documentation_impact(self, content: str) -> Optional[bool]:
        """Extract documentation impact from LLM analysis with LLM fallback"""
        try:
            # First try heuristic extraction
            doc_section = self.extract_section(content, "Documentation Impact")
            if doc_section:
                doc_lower = doc_section.lower()
                if ("not required" in doc_lower or "no updates" in doc_lower or "no impact" in doc_lower or
                    "no documentation updates" in doc_lower):
                    return False
                elif "required" in doc_lower or "should be updated" in doc_lower or "needs update" in doc_lower:
                    return True
            
            # If heuristic failed and we have LLM client, try LLM fallback
            if self.ollama_client:
                self.logger.debug("Heuristic documentation impact extraction failed, trying LLM fallback")
                llm_result = self._extract_metadata_with_llm(content)
                return llm_result.get('documentation_impact')
            
            return None
        except Exception:
            return None
    
    def extract_risk_level(self, content: str) -> Optional[str]:
        """Extract risk level from LLM analysis with LLM fallback"""
        try:
            # First try heuristic extraction
            sections = ["Code Review Recommendation", "Impact Assessment", "Summary", "Recommendations"]
            
            for section_name in sections:
                section_content = self.extract_section(content, section_name)
                if section_content:
                    section_lower = section_content.lower()
                    if ("high risk" in section_lower or "critical" in section_lower or 
                        "breaking change" in section_lower or "major impact" in section_lower):
                        return "HIGH"
                    elif ("medium risk" in section_lower or "moderate" in section_lower or
                          "some impact" in section_lower):
                        return "MEDIUM"
                    elif ("low risk" in section_lower or "minor" in section_lower or
                          "minimal impact" in section_lower or "safe" in section_lower):
                        return "LOW"
            
            # If heuristic failed and we have LLM client, try LLM fallback
            if self.ollama_client:
                self.logger.debug("Heuristic risk level extraction failed, trying LLM fallback")
                llm_result = self._extract_metadata_with_llm(content)
                return llm_result.get('risk_level')
            
            return None
        except Exception as e:
            self.logger.debug(f"Error extracting risk level: {e}")
            return None
    
    def _extract_metadata_with_llm(self, documentation: str) -> Dict[str, Any]:
        """Extract metadata using LLM when heuristics fail"""
        if not self.ollama_client:
            self.logger.warning("No Ollama client available for LLM metadata extraction")
            return {}
        
        try:
            system_prompt = """You are a metadata extraction assistant. Analyze the provided technical documentation and extract specific metadata fields.

Return ONLY a JSON object with these exact fields:
- code_review_recommended: true/false (whether code review is recommended)
- code_review_priority: "HIGH"/"MEDIUM"/"LOW" (priority level if review is recommended, null if not recommended)
- risk_level: "HIGH"/"MEDIUM"/"LOW" (risk level of the changes)
- documentation_impact: true/false (whether documentation needs updates)

Be precise and consistent. If you cannot determine a value, use null."""
            
            prompt = f"""Analyze this technical documentation and extract the metadata:

{documentation}

Return only the JSON object with the metadata fields."""
            
            response = self.ollama_client.call_ollama(prompt, system_prompt)
            
            if response:
                try:
                    # Clean up response and parse JSON
                    cleaned_response = response.strip()
                    if cleaned_response.startswith('```json'):
                        cleaned_response = cleaned_response[7:]
                    if cleaned_response.endswith('```'):
                        cleaned_response = cleaned_response[:-3]
                    cleaned_response = cleaned_response.strip()
                    
                    return json.loads(cleaned_response)
                except json.JSONDecodeError as e:
                    self.logger.warning(f"Failed to parse LLM metadata response as JSON: {e}")
                    return {}
            
            return {}
        except Exception as e:
            self.logger.error(f"Error extracting metadata with LLM: {e}")
            return {}
