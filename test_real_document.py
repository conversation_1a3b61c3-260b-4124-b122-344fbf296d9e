#!/usr/bin/env python3
"""
Test Enhanced Prompts with Real Document Processing
Tests the enhanced prompts with actual document processing workflow
"""

import sys
import logging
from pathlib import Path
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from document_processor import DocumentProcessor
from models import Config


def setup_logging():
    """Setup logging for tests"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


class MockConfigManager:
    """Mock config manager for testing"""
    
    def __init__(self):
        self.config = Config()
        self.config.use_enhanced_prompts = True
        self.config.enhanced_prompts_fallback = True
    
    def load_config(self) -> Config:
        return self.config


def create_test_document():
    """Create a test document file"""
    test_dir = Path("/tmp/test_docs")
    test_dir.mkdir(exist_ok=True)
    
    # Create a test repository directory structure
    repo_dir = test_dir / "test_repo"
    repo_dir.mkdir(exist_ok=True)
    
    # Create a test document with security-related content
    doc_content = """# Revision 789 - Authentication System Enhancement

## Summary
This revision implements a comprehensive OAuth2 authentication system with JWT token support to replace the existing session-based authentication. The change affects core security components and requires careful review.

## Code Review Recommendation
**HIGH PRIORITY** - This change involves critical security components and should be thoroughly reviewed by the security team before deployment.

## Impact Assessment
- **Security**: HIGH - New authentication mechanism affects all user access
- **Performance**: MEDIUM - JWT validation adds minimal overhead
- **Compatibility**: LOW - Backward compatible API maintained
- **Database**: MEDIUM - New token storage tables required

## Risk Level
**HIGH RISK** - Authentication system changes require extensive testing and careful deployment procedures.

## Documentation Impact
**REQUIRED** - API documentation must be updated to reflect new OAuth2 flow and JWT token usage patterns.

## Files Changed
- src/auth/oauth_handler.py (new OAuth2 implementation)
- src/auth/jwt_service.py (JWT token management)
- src/auth/middleware.py (authentication middleware updates)
- config/auth_config.json (OAuth2 configuration)
- database/migrations/add_oauth_tables.sql (database schema)

## Testing Requirements
- Unit tests for OAuth2 flow
- Integration tests for JWT validation
- Security penetration testing
- Load testing for token validation performance

## Deployment Notes
- Requires database migration before deployment
- OAuth2 client credentials must be configured
- Existing sessions will be invalidated during transition
"""
    
    doc_file = repo_dir / "revision_789.md"
    doc_file.write_text(doc_content)
    
    print(f"📄 Created test document: {doc_file}")
    print(f"📝 Document size: {len(doc_content)} characters")
    
    return doc_file


def test_document_processing():
    """Test document processing with enhanced prompts"""
    print("\n=== Testing Real Document Processing ===")
    
    # Create test document
    doc_file = create_test_document()
    
    # Create document processor with mock config
    config_manager = MockConfigManager()
    processor = DocumentProcessor(
        output_dir=str(doc_file.parent.parent),
        config_manager=config_manager
    )
    
    print(f"🔧 Enhanced prompts enabled: {config_manager.config.use_enhanced_prompts}")
    
    # Process the document
    print(f"⚙️  Processing document: {doc_file.name}")
    doc_record = processor._parse_document_file(doc_file)
    
    if doc_record:
        print(f"✅ Document processed successfully!")
        print(f"📊 Document ID: {doc_record.id}")
        print(f"📊 Repository: {doc_record.repository_name}")
        print(f"📊 Revision: {doc_record.revision}")
        print(f"📊 Author: {doc_record.author}")
        print(f"📊 Code Review Recommended: {doc_record.code_review_recommended}")
        print(f"📊 Code Review Priority: {doc_record.code_review_priority}")
        print(f"📊 Risk Level: {doc_record.risk_level}")
        print(f"📊 Documentation Impact: {doc_record.documentation_impact}")
        
        # Verify that enhanced analysis worked
        if (doc_record.code_review_recommended and 
            doc_record.code_review_priority == "HIGH" and
            doc_record.risk_level == "HIGH" and
            doc_record.documentation_impact):
            print(f"🎉 Enhanced analysis appears to be working correctly!")
            print(f"✅ Detected high-risk security change requiring review")
            return True
        else:
            print(f"⚠️  Analysis results may not reflect enhanced prompts")
            return False
    else:
        print(f"❌ Failed to process document")
        return False


def main():
    """Run real document processing test"""
    print("🧪 Enhanced Prompts Real Document Test")
    print("=" * 50)
    
    setup_logging()
    
    try:
        success = test_document_processing()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 Real document test PASSED!")
            print("✅ Enhanced prompts are working with actual document processing")
            print("📈 You should now see improved AI analysis in revision documents")
        else:
            print("⚠️  Real document test completed with warnings")
            print("🔍 Check the analysis results - enhanced prompts may need adjustment")
        
        return success
        
    except Exception as e:
        print(f"\n❌ Real document test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
