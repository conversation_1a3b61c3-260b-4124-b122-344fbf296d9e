# Timeout Configuration for LLM Processing

## Overview

RepoSense AI now includes intelligent timeout management for LLM operations, automatically adjusting timeouts based on model size and complexity.

## ✅ **Implemented Changes**

### **1. Smart Model-Based Timeouts**

The system now automatically determines appropriate timeouts based on model characteristics:

- **Very Large Models (20B+)**: 300 seconds (5 minutes)
  - Examples: `gpt-oss:20b`, `deepseek-coder:33b`
- **Large Models (7B-13B)**: 240 seconds (4 minutes)
  - Examples: `llama3.1:latest`, `qwen3:14b`
- **Medium Models (3B-6B)**: 180 seconds (3 minutes)
  - Examples: `llama3.2:3b`, `qwen2.5:7b`
- **Small Models (1B-2B)**: 120 seconds (2 minutes)
  - Examples: `smollm2:1.7b`
- **Very Small Models (<1B)**: 60 seconds (1 minute)
  - Examples: `smollm:135m`

### **2. Special Model Handling**

Certain models known to be slower receive additional timeout:
- **DeepSeek models**: Base timeout + 60 seconds
- **Codestral models**: Base timeout + 60 seconds

### **3. Configurable Timeout Settings**

New configuration options in `config.json`:

```json
{
  "ollama_timeout_base": 300,        // Base timeout for large models (seconds)
  "ollama_timeout_connection": 30,   // Connection test timeout (seconds)
  "ollama_timeout_embeddings": 90    // Embeddings operation timeout (seconds)
}
```

### **4. Updated Timeout Values**

| Operation | Old Timeout | New Timeout | Notes |
|-----------|-------------|-------------|-------|
| Main LLM calls | 120s | 60-300s | Model-dependent |
| Connection tests | 10s | 30s | Better for remote servers |
| Model queries | 10s | 30s | Configurable |
| Embeddings | 30s | 90s | Configurable |

## **Current Configuration**

For your setup with `gpt-oss:20b` on remote server `192.168.0.77:11434`:

- **Main LLM timeout**: 300 seconds (5 minutes)
- **Connection timeout**: 30 seconds
- **Embeddings timeout**: 90 seconds
- **Model queries**: 30 seconds

## **Benefits**

1. **Reduced timeouts for small models**: Faster failure detection
2. **Increased timeouts for large models**: Prevents premature timeouts
3. **Remote server friendly**: Higher connection timeouts
4. **Configurable**: Easy to adjust via config file
5. **Intelligent**: Automatically adapts to model size

## **Monitoring**

The system now logs timeout information:
```
Testing Ollama connection to http://192.168.0.77:11434/api/tags with timeout 30s
Using timeout of 300 seconds for model gpt-oss:20b
```

## **Troubleshooting**

If you experience timeout issues:

1. **Check logs** for timeout-related messages
2. **Adjust base timeout** in config.json if needed
3. **Consider smaller models** for faster operations
4. **Monitor network latency** to remote Ollama server

## **Future Enhancements**

Potential improvements:
- Dynamic timeout adjustment based on prompt complexity
- Network latency-based timeout scaling
- Per-operation timeout configuration
- Timeout statistics and optimization recommendations
