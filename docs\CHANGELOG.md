# Changelog

All notable changes to the RepoSense AI project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [2.3.0] - 2025-08-10

### 🏢 Enterprise Repository Management
- **Advanced Bulk Operations**: Professional bulk actions for managing multiple repositories simultaneously
  - Enable/disable multiple repositories with single-click operations and detailed feedback
  - Start/stop historical scans across multiple repositories with progress tracking
  - Reset scan status for bulk re-scanning with comprehensive success/error reporting
  - Delete multiple repositories with confirmation dialogs and professional validation
- **Real-Time Status Monitoring**: Live progress tracking with enterprise-grade visual indicators
  - Automatic status updates every 5 seconds during active scans with battery-efficient design
  - Live progress counters showing processed/total revisions with percentage calculations
  - Professional spinner animations with smooth 60fps hardware-accelerated transitions
  - Visual "Live Updates" indicator with smart pause/resume when browser tab is hidden
- **Duplicate Prevention System**: Comprehensive validation to maintain data integrity
  - Client-side validation with immediate feedback and professional Bootstrap styling
  - Server-side validation for authoritative checking with detailed error messages
  - Case-insensitive name checking and URL uniqueness validation with helpful guidance
  - Professional form validation with clear error messages and visual indicators

### 📊 Advanced Document Discovery & Management
- **Comprehensive Search & Filtering**: Multi-field search with professional interface and instant results
  - Real-time search across commit messages, authors, and repositories with 500ms debounce optimization
  - Multi-criteria filtering by repository, author, date range, risk level, code review status, and documentation impact
  - Advanced sorting by date, repository, author, revision, or document size with persistent preferences
  - Collapsible filter panels to maximize content space with professional responsive design
- **Multiple Professional View Modes**: Optimized viewing for different document management workflows
  - **Table View**: Comprehensive document data with all metadata, bulk operations, and sortable columns
  - **Cards View**: Visual document overview with hover effects, enhanced styling, and large previews
  - **Repository Groups**: Documents organized by repository with collapsible sections and document counts
  - Professional responsive design that works seamlessly across all devices and screen sizes
- **Enhanced User Experience**: Modern document management with professional interactions and feedback
  - Auto-submit functionality for instant filter application without page reloads or delays
  - Keyboard shortcuts for efficient navigation (Ctrl+F for search, Escape to clear filters)
  - Professional loading states, smooth transitions, and hover effects throughout the interface

### 🎨 Professional User Interface Enhancements
- **Modern Spinner Animations**: Beautiful circular progress indicators with enterprise-level polish
  - Smooth 60fps hardware-accelerated animations with contextual sizing (16px tables, 24px cards)
  - Interactive hover effects with faster rotation and professional visual depth with subtle shadows
  - Bootstrap color harmony with primary blue theme and gradient effects for enhanced visual appeal
- **Enhanced Repository Views**: Multiple view modes optimized for different management workflows
  - **Table View**: Comprehensive data display with bulk selection capabilities and professional sorting
  - **Cards View**: Visual repository overview with large status indicators and hover effects
  - **Status Groups**: Repositories organized by enabled/disabled status with collapsible sections
  - Responsive design that adapts to all screen sizes with consistent professional experience
- **Keyboard Shortcuts & Accessibility**: Efficient navigation with professional interaction patterns
  - Ctrl+F to focus search fields, Ctrl+A for bulk selection mode, Escape to clear searches
  - Professional keyboard interaction patterns and accessibility compliance throughout

### 🔧 Technical Improvements & Bug Fixes
- **Enhanced Error Messages**: Professional spelling and grammar in all user-facing messages
  - Fixed "start_scand" typo to proper "started scan" with action-specific success messages
  - Comprehensive error handling with helpful guidance and clear next steps for users
- **Real-Time Data Integration**: Live scan status updates with proper enum handling in templates
  - Fixed template conditions to properly handle all enum values (NOT_STARTED, IN_PROGRESS, COMPLETED, FAILED)
  - Merged active scan data with static configuration for accurate real-time status display
  - Enhanced API endpoints to provide live progress data with processed/total revision counts
- **Robust Validation System**: Multi-layer validation for data integrity and user experience
  - Server-side validation with proper error handling and detailed feedback messages
  - Client-side validation with immediate feedback and professional Bootstrap styling
  - Comprehensive input sanitization and security measures throughout the application

### Added
- **Centralized Metadata Extraction Service**: New `MetadataExtractor` class consolidates metadata extraction logic across all services
- **Enhanced Binary File Detection**: Robust content-based binary detection using multiple analysis methods (null bytes, character ratios, file signatures, UTF-8 validation)
- **AI-Generated Commit Messages**: Automatic generation of commit messages from AI summaries when repository commit messages are empty or missing
- **Improved PDF Generation**: Enhanced PDF export with HTML content cleaning and better diff formatting

### Enhanced
- **Side-by-Side Diff Viewer**: Improved inline highlighting and better visual distinction between added/removed content
- **History Scanning Consistency**: Historical scanner now uses same metadata extraction logic as document processor
- **Binary Content Handling**: More accurate detection of binary vs text files, supporting international text files and various encodings
- **PDF Download Reliability**: Fixed PDF generation failures caused by HTML content in diff sections

### Fixed
- **PDF Export Issues**: Resolved ReportLab parsing errors when HTML tags were present in diff content
- **Commit Message Consistency**: Fixed inconsistency between history scanning and document viewing for AI-generated commit messages
- **Binary File Detection**: Replaced heuristic-based detection with robust content analysis for better accuracy

### Technical Improvements
- **Code Consolidation**: Eliminated ~500+ lines of duplicated metadata extraction code across multiple services
- **Single Source of Truth**: Centralized all metadata extraction, date parsing, and document ID generation logic
- **Better Error Handling**: Improved error messages and fallback mechanisms for binary content detection
- **Performance Optimization**: Sample-based binary detection for large files to improve processing speed

### Added
- **Enhanced Side-by-Side Diff Display**: Professional diff visualization with character-level inline highlighting, improved line number formatting (wider columns), and better visual distinction between changes
- **Single Source of Truth Architecture**: Repository backend now serves as the primary data source for commit information, with intelligent fallback to markdown content when repository is unavailable
- **Professional Document Exports**: Complete overhaul of PDF and Markdown downloads with enhanced formatting, emoji icons, table layouts, and proper handling of markdown syntax within diff content
- **Improved Data Consistency**: Document processor now retrieves commit metadata directly from repository backend, ensuring accuracy and consistency across all document views

### Enhanced
- **Diff Visualization**: Side-by-side diff viewer now includes character-level highlighting for precise change identification, wider line number columns (60px) to prevent wrapping, and improved CSS styling
- **PDF Generation**: Complete commit messages are now displayed without truncation, with proper text wrapping and multi-line formatting support
- **Markdown Downloads**: Professional formatting with emoji icons, structured tables, proper code blocks, and HTML pre-blocks for diff content to prevent markdown interpretation issues
- **Error Handling**: Graceful degradation when repository backend is unavailable, with clear user messaging about data source limitations

### Fixed
- **Diff Content in Markdown**: Fixed issue where diff content containing markdown syntax was being interpreted rather than displayed literally by using HTML pre-blocks with proper entity escaping
- **Commit Message Display**: Resolved truncation issues in PDF exports where long commit messages were cut off with continuation indicators
- **Data Source Consistency**: Eliminated discrepancies between different views of the same document by establishing repository backend as single source of truth
- **Template Syntax**: Fixed Jinja2 template syntax errors that were causing 500 errors when viewing documents with diff content
- **PDF Generation**: Fixed parameter name mismatch in PDF generator method call that was causing PDF download failures
- **Null Safety**: Enhanced diff service with proper null checks for config manager to prevent crashes in edge cases
- **Variable Naming**: Resolved variable naming conflict in user assignment logic that could cause confusion
- **Code Quality**: Improved type hints and removed unused imports for better maintainability

### Enhanced
- **Docker Architecture**: Unified Docker setup by removing docker-compose.override.yml and integrating development features into main compose file with conditional environment variables
- **Development Workflow**: Simplified development mode activation through .env file with REPOSENSE_AI_LOG_LEVEL=DEBUG and REPOSENSE_AI_DB_DEBUG=true
- **Command Interface**: Removed Makefile to maintain truly unified approach - all operations now use docker-compose commands directly
- **Documentation Consistency**: Updated all documentation to use docker-compose commands instead of Make commands for better cross-platform compatibility
- **Repository Backend Architecture**: Added abstract file browsing interface to support future Git integration
- **Error Handling**: Improved robustness of diff generation when configuration is unavailable

### Added
- **Professional PDF Generation**: Complete PDF export system with syntax-highlighted diffs, comprehensive metadata, and professional formatting
- **Enhanced Repository Discovery**: Intelligent SVN discovery with SSL certificate support, protocol fallback (HTTPS/HTTP), and comprehensive error handling
- **Dual Timestamp Tracking**: Separate tracking for commit dates (when changes were made) and processing dates (when RepoSense AI analyzed them)
- **Advanced Branch Detection**: Automatic discovery and categorization of trunk, branches, and tags within repositories
- **Enhanced Repository Interface**: Improved discovery UI with branch filtering, search functionality, and responsive design
- **Comprehensive SSL Support**: Full support for self-signed certificates and problematic SSL configurations with automatic fallback
- **Enhanced Document Downloads**: Multiple download formats (Markdown, PDF) with professional formatting
- **PDF Generation**: Full-featured PDF export with syntax-highlighted diffs and AI processing information
- **AI Processing Transparency**: Dedicated section showing AI model, processing time, and analysis results
- **Dual Timestamp Tracking**: Separate timestamps for commit date (from repository) and processing date (by RepoSense AI)
- **Repository Status Refresh**: Manual refresh button for repository status updates
- **Enhanced Diff Visualization**: Color-coded unified diffs with proper syntax highlighting
- **Commit Message Formatting**: Improved display with proper line breaks and bullet point formatting
- **Environment Variable Overrides**: Configuration values can be overridden by environment variables
- **Advanced SVN Discovery**: Intelligent repository discovery with protocol fallback and SSL handling
- **SVN Branch Detection**: Automatic discovery of trunk, branches, and tags within repositories
- **SSL Certificate Support**: Comprehensive handling of self-signed and problematic SSL certificates
- **Protocol Fallback**: Automatic fallback between HTTPS, HTTP, and svn:// protocols
- **User Documentation Input & Augmentation**: Complete system for users to enhance AI-generated documentation with additional content and suggestions
- **Interactive Repository File Browser**: Visual file browser for selecting product documentation files during repository setup
- **AI-Powered Documentation Suggestions**: Specialized AI analysis for generating user-facing product documentation content
- **Multi-Format Document Processing**: Support for Word (.doc/.docx), RTF, OpenDocument Text (.odt), and other document formats
- **Enhanced PDF Generation**: Complete user feedback inclusion in PDF downloads with professional formatting
- **Asynchronous AI Processing**: Non-blocking AI suggestion generation for improved user experience
- **Document Format Support**: Extended support for Microsoft Office, RTF, and OpenDocument formats in product documentation discovery

### Enhanced
- **Repository Discovery**: Comprehensive SSL certificate handling with automatic protocol fallback for maximum compatibility
- **Error Handling**: Improved error messages and graceful degradation for connection issues and SSL problems
- **User Interface**: Enhanced repository discovery with branch type indicators, search filtering, and responsive mobile design
- **Connection Reliability**: Robust handling of self-signed certificates, expired certificates, and various SSL configuration issues
- **Model Availability Checking**: Proactive validation of AI model availability with clear error messages and fallback suggestions
- **Repository Management**: Added commit date and processed date columns with clear visual indicators, plus product documentation file configuration
- **Document View**: Enhanced with AI processing information section, user feedback forms, and improved download options with complete user input preservation
- **User Experience**: Interactive file browsers, real-time AI suggestions, and comprehensive feedback collection systems
- **Configuration System**: Simplified to single config file (`data/config.json`) with web-based management
- **PDF Quality**: Professional formatting with proper markdown parsing, tables, and typography
- **Diff Rendering**: Both web interface and PDF exports now have enhanced diff formatting
- **Download Experience**: Dropdown menu with multiple format options and test functionality
- **SVN Backend Robustness**: Comprehensive error handling, timeout management, and connection resilience
- **Repository Discovery**: Multi-protocol support with intelligent server type detection
- **SSL/TLS Handling**: Enhanced support for self-signed certificates and problematic SSL configurations
- **XML Parsing**: Robust parsing of various SVN server XML formats (VisualSVN, Apache DAV, etc.)

### Fixed
- **SSL Certificate Issues**: Resolved connection problems with self-signed certificates, expired certificates, and SSL verification failures
- **Repository Discovery**: Fixed timeout and connection issues with various SVN server configurations and SSL setups
- **Protocol Compatibility**: Automatic fallback handling for servers that don't support HTTPS or have SSL configuration issues
- **Model Validation**: Proactive checking of AI model availability to prevent processing failures
- **Error Response Codes**: Corrected HTTP response code handling to prevent duplicate status codes in API responses
- **Repository Configuration**: Improved handling of repository imports with automatic historical scan configuration
- **Configuration Loading**: Streamlined config path resolution to only use data directory
- **PDF Generation**: Resolved HTML tag parsing issues, missing styles, and improved content formatting with complete user feedback inclusion
- **Repository Timestamps**: Accurate tracking of both source control and processing timestamps
- **Download Functionality**: Resolved browser compatibility issues with automatic downloads and JavaScript syntax errors
- **User Feedback Integration**: Fixed data flow from database to templates to ensure all user input is preserved in downloads
- **Document Model Completeness**: Added missing user feedback fields to ensure complete data persistence
- **Repository Edit Functionality**: Fixed JavaScript parameter passing issues that prevented repository editing
- **SVN SSL Issues**: Resolved connection problems with self-signed certificates and SSL verification
- **Repository Discovery**: Fixed timeout and connection issues with various SVN server configurations
- **Protocol Compatibility**: Automatic fallback handling for servers that don't support HTTP/DAV
- **XML Parsing Errors**: Robust handling of malformed or incomplete XML responses from SVN servers

### Changed
- **Configuration Structure**: Consolidated from multiple config files to single `data/config.json`
- **File Organization**: Removed obsolete config templates and updated all references
- **Documentation**: Updated all setup instructions to reflect simplified configuration
- **Build Process**: Updated binary packaging to exclude obsolete configuration files

### Removed
- **Obsolete Config Files**: Removed `config.example.json`, `config.minimal.json`, and root-level `config.json`
- **Legacy Docker Compose**: Removed multiple compose files in favor of single `docker-compose.yml`
- **Redundant Scripts**: Cleaned up scripts that referenced old configuration structure

## [Previous Releases] - 2025-08-04

### Added
- **User Feedback System**: Complete workflow for code review tracking, documentation quality ratings, and risk assessment overrides
- **Side-by-Side Diff Viewer**: HTML table-based diff rendering with format switching capabilities
- **Multi-Encoding Support**: Automatic detection and handling of UTF-8, Latin-1, CP1252, and ISO-8859-1 encodings
- **Binary File Detection**: Smart detection of binary content (PDFs, images) with appropriate user messaging
- **Hybrid AI Analysis**: Fast heuristic pattern matching with LLM fallback for robust metadata extraction
- **On-Demand Diff Generation**: Dynamic diff creation using stored repository metadata instead of large content storage
- **Progress Calculation Fix**: Accurate progress tracking for revision ranges not starting from revision 1
- **Repository Metadata Storage**: Efficient storage of repository URL and type for diff recreation
- **Enhanced Error Handling**: Comprehensive error recovery and meaningful user messages
- **Comprehensive Test Suite**: Multiple test files covering encoding, progress, user feedback, and diff functionality

### Changed
- **Database Schema**: Added 12 new fields for user feedback (code review, documentation ratings, risk assessments)
- **DiffService Architecture**: Modified to use repository credentials and handle multiple encodings
- **Progress Display Logic**: Updated JavaScript and backend to show position within selected range
- **Document Storage Strategy**: Changed from storing diff content to storing metadata for on-demand generation
- **AI Analysis Approach**: Enhanced with hybrid heuristic + LLM fallback methodology
- **Subprocess Handling**: Updated to use bytes instead of text for proper encoding control

### Fixed
- **Critical UTF-8 Encoding Bug**: Resolved "utf-8 codec can't decode byte 0xe2" crashes when processing binary files
- **Progress Calculation Error**: Fixed incorrect percentage display (715% instead of proper percentage) for non-starting revision ranges
- **SVN Authentication Issues**: Resolved diff service authentication by integrating repository configuration credentials
- **Database Schema Mismatches**: Fixed container crashes due to missing database columns
- **JavaScript Progress Display**: Corrected frontend progress calculation to use processed revisions instead of absolute revision numbers
- **Binary File Handling**: Proper detection and messaging for non-text files instead of system crashes

### Security
- **Credential Management**: Enhanced secure handling of repository authentication in diff generation
- **Input Validation**: Added comprehensive validation for user feedback inputs and API parameters
- **Error Message Sanitization**: Ensured error messages don't expose sensitive system information

### Performance
- **Reduced Database Storage**: On-demand diff generation eliminates need to store large diff content
- **Efficient Encoding Detection**: Early binary file detection prevents unnecessary processing
- **Optimized AI Analysis**: Fast heuristics with LLM fallback only when necessary
- **Memory Management**: Proper cleanup of subprocess resources and efficient metadata storage

### Developer Experience
- **Comprehensive Testing**: Added multiple test files for all major functionality
- **Enhanced Documentation**: Updated development guide with recent changes and testing approaches
- **Error Debugging**: Improved error messages and logging for better troubleshooting
- **Code Quality**: Enhanced error handling and graceful degradation throughout the system

## [2.0.0] - 2025-08-02

### Added
- **Application Rebranding**: Changed from "SVN Monitor" to "RepoSense AI"
- **Plugin Architecture**: Extensible backend system with abstract base classes
- **Document Management System**: AI-generated documentation with web interface
- **Enhanced Web Interface**: Modern Bootstrap 5 design with responsive layout
- **Configuration Management**: Flexible JSON-based configuration system
- **Docker Development Environment**: Hot-reload development with containerization
- **Comprehensive Documentation**: Complete setup, development, and deployment guides

### Changed
- **Architecture**: Transformed from monolithic to modular plugin-based system
- **File Structure**: Reorganized into logical service-based components
- **User Interface**: Complete redesign with modern styling and navigation
- **Configuration**: Enhanced with auto-detection and validation features

### Fixed
- **JavaScript DOM Errors**: Resolved element selector issues in web interface
- **Repository Discovery**: Enhanced XML parsing for VisualSVN compatibility
- **Error Handling**: Comprehensive error recovery and user feedback

### Security
- **User Management**: Role-based access control with proper enum handling
- **Input Validation**: Enhanced validation throughout the system
- **Configuration Security**: Secure handling of repository credentials

## [1.0.0] - Initial Release

### Added
- **Basic SVN Monitoring**: Core SVN repository monitoring functionality
- **Web Interface**: Simple web-based monitoring dashboard
- **Configuration**: Basic configuration management
- **Docker Support**: Initial containerization support

---

## Legend

- **Added**: New features
- **Changed**: Changes in existing functionality  
- **Deprecated**: Soon-to-be removed features
- **Removed**: Removed features
- **Fixed**: Bug fixes
- **Security**: Security improvements
- **Performance**: Performance improvements
- **Developer Experience**: Improvements for developers
