# RepoSense AI - Production Binary Deployment
# Multi-stage build for optimized binary distribution

# Stage 1: Build Environment
FROM python:3.11-slim as builder

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    libffi-dev \
    libssl-dev \
    subversion \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install PyInstaller for binary creation
RUN pip install pyinstaller

# Copy application source code
COPY . .

# Create binary using PyInstaller
RUN pyinstaller --onefile \
    --name reposense-ai \
    --add-data "templates:templates" \
    --add-data "static:static" \
    --add-data "docs:docs" \
    --add-data "marketing:marketing" \
    --hidden-import=repository_backends.svn_backend \
    --hidden-import=repository_backends.git_backend \
    --hidden-import=repository_backends.base \
    --hidden-import=flask \
    --hidden-import=sqlite3 \
    --hidden-import=requests \
    --hidden-import=jinja2 \
    --collect-all=flask \
    --collect-all=jinja2 \
    reposense_ai_app.py

# Stage 2: Runtime Environment
FROM ubuntu:22.04 as runtime

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    subversion \
    git \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN groupadd -r reposense-ai && \
    useradd -r -g reposense-ai -d /app -s /bin/bash reposense-ai

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/dist/reposense-ai /app/reposense-ai

# Copy configuration templates and static files
COPY --from=builder /app/config.example.json /app/config.example.json
COPY --from=builder /app/templates /app/templates
COPY --from=builder /app/static /app/static
COPY --from=builder /app/docs /app/docs
COPY --from=builder /app/marketing /app/marketing

# Create data directory and set permissions
RUN mkdir -p /app/data /app/logs && \
    chown -R reposense-ai:reposense-ai /app

# Create startup script
RUN cat > /app/start.sh << 'EOF'
#!/bin/bash
set -e

# Initialize configuration if not exists
if [ ! -f /app/data/config.json ]; then
    echo "Initializing configuration..."
    cp /app/config.example.json /app/data/config.json
    echo "Configuration initialized. Please customize /app/data/config.json"
fi

# Start RepoSense AI
echo "Starting RepoSense AI..."
exec /app/reposense-ai
EOF

RUN chmod +x /app/start.sh && \
    chown reposense-ai:reposense-ai /app/start.sh

# Switch to non-root user
USER reposense-ai

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV REPOSENSE_AI_CONFIG=/app/data/config.json
ENV REPOSENSE_AI_DATA_DIR=/app/data
ENV REPOSENSE_AI_LOG_DIR=/app/logs

# Start the application
CMD ["/app/start.sh"]
