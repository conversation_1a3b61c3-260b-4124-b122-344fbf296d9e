#!/usr/bin/env python3
"""
Web interface for the RepoSense AI application
Provides a Flask-based web UI for configuration and monitoring
"""

import logging
import re
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, make_response
from markupsafe import Markup

from config_manager import ConfigManager
from monitor_service import MonitorService
from models import Config, RepositoryConfig, User, UserRole, HistoricalScanConfig, HistoricalScanStatus
from user_management_service import UserManagementService
from repository_backends import get_backend_manager
from repository_backends.base import RepositoryBackend
from typing import Optional
from document_service import DocumentService, Document
from historical_scanner import HistoricalScanner


class WebInterface:
    """Web interface for configuration and monitoring"""
    
    def __init__(self, monitor_service: MonitorService):
        self.monitor_service = monitor_service
        self.config_manager = monitor_service.config_manager
        self.file_manager = monitor_service.file_manager

        # Initialize new services
        self.user_service = UserManagementService(monitor_service.config)
        self.backend_manager = get_backend_manager()
        self.document_service = DocumentService(
            monitor_service.config.output_dir,
            ollama_client=monitor_service.ollama_client,
            config_manager=monitor_service.config_manager
        )

        # Initialize historical scanner (create a simple document processor for it)
        from document_processor import DocumentProcessor
        document_processor = DocumentProcessor(
            monitor_service.config.output_dir,
            "/app/data/documents.db",
            monitor_service.config_manager
        )

        self.historical_scanner = HistoricalScanner(
            self.backend_manager,
            document_processor,
            monitor_service.ollama_client,
            db_path="/app/data/documents.db",
            config_manager=monitor_service.config_manager,
            monitor_service=monitor_service
        )
        self.historical_scanner.start()

        self.app = Flask(__name__)
        self.app.secret_key = monitor_service.config.web_secret_key
        self.logger = logging.getLogger(__name__)

        # Register custom Jinja2 filters
        self.app.jinja_env.filters['markdown'] = self.markdown_to_html

        self.setup_routes()

    def markdown_to_html(self, text):
        """Convert markdown text to HTML using simple regex replacements"""
        if not text:
            return ""

        # Escape HTML characters first
        html = text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

        # Convert markdown syntax to HTML
        # Headers
        html = re.sub(r'^# (.+)$', r'<h1>\1</h1>', html, flags=re.MULTILINE)
        html = re.sub(r'^## (.+)$', r'<h2>\1</h2>', html, flags=re.MULTILINE)
        html = re.sub(r'^### (.+)$', r'<h3>\1</h3>', html, flags=re.MULTILINE)
        html = re.sub(r'^#### (.+)$', r'<h4>\1</h4>', html, flags=re.MULTILINE)

        # Bold and italic
        html = re.sub(r'\*\*(.+?)\*\*', r'<strong>\1</strong>', html)
        html = re.sub(r'\*(.+?)\*', r'<em>\1</em>', html)

        # Code blocks
        html = re.sub(r'```(\w+)?\n(.*?)\n```', r'<pre><code class="language-\1">\2</code></pre>', html, flags=re.DOTALL)
        html = re.sub(r'`(.+?)`', r'<code>\1</code>', html)

        # Lists
        html = re.sub(r'^- (.+)$', r'<li>\1</li>', html, flags=re.MULTILINE)
        html = re.sub(r'(<li>.*</li>)', r'<ul>\1</ul>', html, flags=re.DOTALL)
        html = re.sub(r'</ul>\s*<ul>', '', html)  # Merge consecutive lists

        # Line breaks
        html = re.sub(r'\n\n', '</p><p>', html)
        html = re.sub(r'\n', '<br>', html)

        # Wrap in paragraphs
        if html and not html.startswith('<'):
            html = f'<p>{html}</p>'

        return Markup(html)

    def setup_routes(self):
        """Setup Flask routes"""

        @self.app.route('/health')
        def health_check():
            """Health check endpoint for Docker and load balancers"""
            try:
                # Check if monitor service is responsive
                status = self.monitor_service.get_status()

                # Check database connectivity
                try:
                    self.document_service.get_documents(limit=1)
                    db_status = "healthy"
                except Exception as e:
                    db_status = f"error: {str(e)}"

                # Check Ollama connectivity (if configured)
                ollama_status = "not_configured"
                if hasattr(self.monitor_service, 'ollama_client') and self.monitor_service.ollama_client:
                    try:
                        models = self.monitor_service.ollama_client.get_available_models()
                        ollama_status = "healthy" if models else "no_models"
                    except Exception as e:
                        ollama_status = f"error: {str(e)}"

                health_data = {
                    "status": "healthy",
                    "timestamp": status.get("current_time", "unknown"),
                    "version": "2.1.0",
                    "services": {
                        "monitor": "healthy" if status.get("is_running", False) else "stopped",
                        "database": db_status,
                        "ollama": ollama_status
                    },
                    "uptime": status.get("uptime", "unknown")
                }

                # Return 200 if all critical services are healthy
                http_status = 200 if db_status == "healthy" else 503
                return jsonify(health_data), http_status

            except Exception as e:
                return jsonify({
                    "status": "unhealthy",
                    "error": str(e),
                    "timestamp": "unknown"
                }), 503

        @self.app.route('/')
        def index():
            import time
            start_time = time.time()
            status = self.monitor_service.get_status()
            load_time = time.time() - start_time
            self.logger.debug(f"Dashboard loaded in {load_time:.3f}s")
            return render_template('index.html', status=status, config=self.monitor_service.config)
        
        @self.app.route('/config')
        def config_page():
            # Get available models for fallback
            available_models = self.monitor_service.get_available_models()
            return render_template('config.html',
                                 config=self.monitor_service.config,
                                 available_models=available_models)
        
        @self.app.route('/config/save', methods=['POST'])
        def save_config():
            try:
                # Update config from form data
                new_config = self.config_manager.update_config_from_form(
                    self.monitor_service.config, 
                    request.form
                )
                
                # Validate config
                is_valid, errors = self.config_manager.validate_config(new_config)
                if not is_valid:
                    for error in errors:
                        flash(error, 'error')
                    return redirect(url_for('config_page'))
                
                # Update monitor service with new config
                self.monitor_service.update_config(new_config)
                
                flash('Configuration saved successfully!', 'success')
                
            except Exception as e:
                flash(f'Error saving configuration: {str(e)}', 'error')
            
            return redirect(url_for('config_page'))
        
        @self.app.route('/api/status')
        def api_status():
            import time
            start_time = time.time()
            status = self.monitor_service.get_status()
            load_time = time.time() - start_time
            status['load_time_ms'] = round(load_time * 1000, 2)
            return jsonify(status)

        @self.app.route('/api/performance')
        def api_performance():
            """Get performance metrics"""
            import time
            return jsonify({
                'ollama_cache_age_seconds': time.time() - (self.monitor_service.ollama_cache_time or 0) if self.monitor_service.ollama_cache_time else None,
                'ollama_cache_duration_seconds': self.monitor_service.ollama_cache_duration,
                'ollama_cached_status': self.monitor_service.ollama_connected_cache
            })

        @self.app.route('/api/ollama/models')
        def api_ollama_models():
            """Get available Ollama models"""
            models = self.monitor_service.get_available_models()
            return jsonify({
                'models': models,
                'count': len(models),
                'connected': self.monitor_service.get_ollama_connection_status()
            })
        
        @self.app.route('/api/start', methods=['POST'])
        def api_start():
            enabled_repos = self.monitor_service.config.get_enabled_repositories()
            if not enabled_repos:
                return jsonify({'error': 'No repositories configured or enabled'}), 400

            self.monitor_service.start_monitoring()
            return jsonify({'status': 'started', 'repositories': len(enabled_repos)})
        
        @self.app.route('/api/stop', methods=['POST'])
        def api_stop():
            self.monitor_service.stop_monitoring()
            return jsonify({'status': 'stopped'})
        
        @self.app.route('/api/check', methods=['POST'])
        def api_check():
            enabled_repos = self.monitor_service.config.get_enabled_repositories()
            if not enabled_repos:
                return jsonify({'error': 'No repositories configured or enabled'}), 400

            try:
                self.monitor_service.run_once()
                return jsonify({'status': 'check completed', 'repositories_checked': len(enabled_repos)})
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/logs')
        def logs_page():
            try:
                log_entries_count = self.monitor_service.config.web_log_entries
                logs = self.file_manager.read_recent_logs(log_entries_count)
                return render_template('logs.html', logs=logs, log_entries_count=log_entries_count)
            except Exception as e:
                return render_template('logs.html', logs=[f'Error reading logs: {str(e)}'], log_entries_count=100)
        
        @self.app.route('/api/test_ollama')
        def api_test_ollama():
            connected = self.monitor_service.ollama_client.test_connection()
            return jsonify({'connected': connected})

        # Repository management routes
        @self.app.route('/repositories')
        def repositories_page():
            # Force reload config to get latest data
            self.monitor_service.config = self.monitor_service.config_manager.load_config()

            # Get filter and sort parameters
            search_query = request.args.get('search', '').strip()
            status_filter = request.args.get('status', '')  # enabled, disabled, all
            type_filter = request.args.get('type', '')  # git, svn, all
            scan_status_filter = request.args.get('scan_status', '')  # completed, active, failed, all
            sort_by = request.args.get('sort_by', 'name')  # name, type, status, last_revision, last_commit_date
            sort_order = request.args.get('sort_order', 'asc')  # asc, desc
            view_mode = request.args.get('view_mode', 'table')  # table, cards, status_groups

            # Start with all repositories and merge active scan data
            repositories = []
            for repo in self.monitor_service.config.repositories:
                # Create a copy of the repository to avoid modifying the original
                repo_copy = repo

                # Update scan status from active/completed scans if available
                if repo.historical_scan:
                    scan_progress = None
                    if repo.id in self.historical_scanner.active_scans:
                        scan_progress = self.historical_scanner.active_scans[repo.id]
                    elif repo.id in self.historical_scanner.completed_scans:
                        scan_progress = self.historical_scanner.completed_scans[repo.id]

                    if scan_progress:
                        # Update the historical scan with current progress
                        repo.historical_scan.scan_status = scan_progress.status
                        repo.historical_scan.processed_revisions = scan_progress.processed_revisions
                        repo.historical_scan.total_revisions = scan_progress.total_revisions

                repositories.append(repo_copy)

            # Apply search filter
            if search_query:
                repositories = [repo for repo in repositories
                              if search_query.lower() in repo.name.lower()
                              or search_query.lower() in repo.url.lower()
                              or (repo.username and search_query.lower() in repo.username.lower())]

            # Apply status filter
            if status_filter == 'enabled':
                repositories = [repo for repo in repositories if repo.enabled]
            elif status_filter == 'disabled':
                repositories = [repo for repo in repositories if not repo.enabled]

            # Apply type filter
            if type_filter and type_filter != 'all':
                repositories = [repo for repo in repositories if repo.type == type_filter]

            # Apply scan status filter
            if scan_status_filter and scan_status_filter != 'all':
                repositories = [repo for repo in repositories
                              if repo.historical_scan and repo.historical_scan.scan_status
                              and repo.historical_scan.scan_status.value == scan_status_filter]

            # Apply sorting
            reverse_order = sort_order == 'desc'
            if sort_by == 'name':
                repositories.sort(key=lambda r: r.name.lower(), reverse=reverse_order)
            elif sort_by == 'type':
                repositories.sort(key=lambda r: r.type, reverse=reverse_order)
            elif sort_by == 'status':
                repositories.sort(key=lambda r: r.enabled, reverse=reverse_order)
            elif sort_by == 'last_revision':
                repositories.sort(key=lambda r: r.last_revision or 0, reverse=reverse_order)
            elif sort_by == 'last_commit_date':
                repositories.sort(key=lambda r: r.last_commit_date or '', reverse=reverse_order)

            # Get filter options for dropdowns
            available_types = list(set(repo.type for repo in self.monitor_service.config.repositories))
            available_scan_statuses = list(set(
                repo.historical_scan.scan_status.value for repo in self.monitor_service.config.repositories
                if repo.historical_scan and repo.historical_scan.scan_status
            ))

            return render_template('repositories.html',
                                 repositories=repositories,
                                 config=self.monitor_service.config,
                                 # Current filters
                                 search_query=search_query,
                                 status_filter=status_filter,
                                 type_filter=type_filter,
                                 scan_status_filter=scan_status_filter,
                                 sort_by=sort_by,
                                 sort_order=sort_order,
                                 view_mode=view_mode,
                                 # Filter options
                                 available_types=available_types,
                                 available_scan_statuses=available_scan_statuses,
                                 total_count=len(repositories),
                                 total_repositories=len(self.monitor_service.config.repositories))

        @self.app.route('/repositories/add', methods=['POST'])
        def add_repository():
            try:
                repo = self.config_manager.add_repository_from_form(
                    self.monitor_service.config,
                    request.form
                )
                self.monitor_service.save_config()
                self.monitor_service.file_manager.setup_directories()  # Create new repo directories
                flash(f'Repository "{repo.name}" added successfully!', 'success')
            except Exception as e:
                flash(f'Error adding repository: {str(e)}', 'error')

            return redirect(url_for('repositories_page'))

        @self.app.route('/repositories/<repo_id>/edit', methods=['POST'])
        def edit_repository(repo_id):
            try:
                success = self.config_manager.update_repository_from_form(
                    self.monitor_service.config,
                    repo_id,
                    request.form
                )
                if success:
                    self.monitor_service.save_config()
                    flash('Repository updated successfully!', 'success')
                else:
                    flash('Repository not found!', 'error')
            except Exception as e:
                flash(f'Error updating repository: {str(e)}', 'error')

            return redirect(url_for('repositories_page'))

        @self.app.route('/repositories/<repo_id>/delete', methods=['POST'])
        def delete_repository(repo_id):
            try:
                repo = self.monitor_service.config.get_repository_by_id(repo_id)
                if repo:
                    repo_name = repo.name
                    if self.monitor_service.config.remove_repository(repo_id):
                        self.monitor_service.save_config()
                        flash(f'Repository "{repo_name}" deleted successfully!', 'success')
                    else:
                        flash('Failed to delete repository!', 'error')
                else:
                    flash('Repository not found!', 'error')
            except Exception as e:
                flash(f'Error deleting repository: {str(e)}', 'error')

            return redirect(url_for('repositories_page'))

        @self.app.route('/repositories/bulk-action', methods=['POST'])
        def bulk_repository_action():
            """Handle bulk actions on repositories"""
            try:
                action = request.form.get('action')
                selected_repos = request.form.getlist('selected_repos')

                if not selected_repos:
                    flash('No repositories selected', 'warning')
                    return redirect(url_for('repositories_page'))

                success_count = 0
                error_count = 0

                for repo_id in selected_repos:
                    try:
                        repo = self.monitor_service.config.get_repository_by_id(repo_id)
                        if not repo:
                            error_count += 1
                            continue

                        if action == 'enable':
                            repo.enabled = True
                            success_count += 1
                        elif action == 'disable':
                            repo.enabled = False
                            success_count += 1
                        elif action == 'delete':
                            self.monitor_service.config.remove_repository(repo_id)
                            success_count += 1
                        elif action == 'start_scan':
                            if repo.historical_scan and repo.historical_scan.enabled:
                                # Queue historical scan
                                scan_config = repo.historical_scan
                                queued = self.historical_scanner.queue_scan(repo, scan_config)
                                if queued:
                                    success_count += 1
                                else:
                                    # Check if there's a specific error message in completed_scans
                                    if repo.id in self.historical_scanner.completed_scans:
                                        progress = self.historical_scanner.completed_scans[repo.id]
                                        if progress.error_message:
                                            self.logger.warning(f"Failed to start scan for {repo.name}: {progress.error_message}")
                                    error_count += 1
                            else:
                                self.logger.warning(f"Cannot start scan for {repo.name}: Historical scan not configured or disabled")
                                error_count += 1
                        elif action == 'stop_scan':
                            cancelled = self.historical_scanner.cancel_scan(repo_id)
                            if cancelled:
                                success_count += 1
                            else:
                                error_count += 1
                        elif action == 'reset_status':
                            if repo.historical_scan:
                                # Reset scan status and progress
                                repo.historical_scan.scan_status = HistoricalScanStatus.NOT_STARTED
                                repo.historical_scan.last_scanned_revision = None
                                repo.historical_scan.scan_started_at = None
                                repo.historical_scan.scan_completed_at = None
                                repo.historical_scan.processed_revisions = 0
                                repo.historical_scan.failed_revisions = 0
                                repo.historical_scan.total_revisions = None
                                repo.historical_scan.error_message = None

                                # Also remove from scanner's completed scans if present
                                if repo_id in self.historical_scanner.completed_scans:
                                    del self.historical_scanner.completed_scans[repo_id]

                                success_count += 1
                                self.logger.info(f"Reset scan status for repository {repo.name}")
                            else:
                                self.logger.warning(f"Cannot reset scan status for {repo.name}: No historical scan configuration")
                                error_count += 1
                        else:
                            error_count += 1

                    except Exception as e:
                        self.logger.error(f"Error processing bulk action {action} for repo {repo_id}: {e}")
                        error_count += 1

                # Save configuration if any changes were made
                if action in ['enable', 'disable', 'delete', 'reset_status']:
                    self.monitor_service.save_config()

                # Show results
                if success_count > 0:
                    if action == 'reset_status':
                        flash(f'Successfully reset scan status for {success_count} repositories. You can now start new scans.', 'success')
                    elif action == 'start_scan':
                        flash(f'Successfully started scan for {success_count} repositories', 'success')
                    elif action == 'stop_scan':
                        flash(f'Successfully stopped scan for {success_count} repositories', 'success')
                    elif action == 'enable':
                        flash(f'Successfully enabled {success_count} repositories', 'success')
                    elif action == 'disable':
                        flash(f'Successfully disabled {success_count} repositories', 'success')
                    elif action == 'delete':
                        flash(f'Successfully deleted {success_count} repositories', 'success')
                    else:
                        flash(f'Successfully processed {success_count} repositories', 'success')
                if error_count > 0:
                    if action == 'start_scan':
                        flash(f'Failed to start scan for {error_count} repositories. '
                              f'This usually means the scan is already completed or no new revisions are available. '
                              f'Check the Historical Scan page for more details or use "Reset Status" to re-scan.', 'warning')
                    elif action == 'reset_status':
                        flash(f'Failed to reset scan status for {error_count} repositories. Check that they have historical scan configured.', 'error')
                    else:
                        flash(f'Failed to {action} {error_count} repositories', 'error')

            except Exception as e:
                flash(f'Error performing bulk action: {str(e)}', 'error')

            return redirect(url_for('repositories_page'))

        @self.app.route('/api/repositories/status')
        def api_repositories_status():
            """API endpoint for real-time repository status updates"""
            try:
                # Force reload config to get latest data
                self.monitor_service.config = self.monitor_service.config_manager.load_config()

                repositories_data = []
                for repo in self.monitor_service.config.repositories:
                    # Get current scan progress if available
                    scan_progress = None
                    if repo.id in self.historical_scanner.active_scans:
                        scan_progress = self.historical_scanner.active_scans[repo.id]
                    elif repo.id in self.historical_scanner.completed_scans:
                        scan_progress = self.historical_scanner.completed_scans[repo.id]

                    # Build repository data
                    repo_data = {
                        'id': repo.id,
                        'name': repo.name,
                        'enabled': repo.enabled,
                        'historical_scan': None
                    }

                    if repo.historical_scan:
                        # Update scan status from active/completed scans if available
                        current_status = repo.historical_scan.scan_status
                        processed_revisions = repo.historical_scan.processed_revisions
                        total_revisions = repo.historical_scan.total_revisions

                        if scan_progress:
                            current_status = scan_progress.status
                            processed_revisions = scan_progress.processed_revisions
                            total_revisions = scan_progress.total_revisions

                        repo_data['historical_scan'] = {
                            'enabled': repo.historical_scan.enabled,
                            'scan_status': {
                                'value': current_status.value if hasattr(current_status, 'value') else current_status
                            },
                            'processed_revisions': processed_revisions,
                            'total_revisions': total_revisions,
                            'last_scanned_revision': repo.historical_scan.last_scanned_revision
                        }

                    repositories_data.append(repo_data)

                return jsonify(repositories_data)

            except Exception as e:
                self.logger.error(f"Error getting repository status: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/repositories')
        def api_repositories():
            return jsonify({
                'repositories': [
                    {
                        'id': repo.id,
                        'name': repo.name,
                        'url': repo.url,
                        'enabled': repo.enabled,
                        'last_revision': repo.last_revision
                    }
                    for repo in self.monitor_service.config.repositories
                ]
            })

        @self.app.route('/api/repositories/<repo_id>/revisions')
        def api_repository_revisions(repo_id):
            """Get available revisions for a repository"""
            try:
                # Find repository
                repo = None
                for r in self.monitor_service.config.repositories:
                    if r.id == repo_id:
                        repo = r
                        break

                if not repo:
                    return jsonify({'error': 'Repository not found'}), 404

                # Get backend
                backend = self.backend_manager.get_backend_for_repository(repo, None)
                if not backend:
                    return jsonify({'error': 'No backend available for repository'}), 500

                # Get latest revision
                latest_revision = backend.get_latest_revision(repo)
                if not latest_revision:
                    return jsonify({'error': 'Could not determine latest revision'}), 500

                try:
                    latest_num = int(latest_revision)
                    # Generate list of all available revisions
                    revisions = []
                    for rev_num in range(1, latest_num + 1):
                        revisions.append({
                            'number': rev_num,
                            'display': f"r{rev_num}"
                        })

                    return jsonify({
                        'revisions': revisions,
                        'latest_revision': latest_num,
                        'total_count': latest_num
                    })

                except ValueError:
                    # Non-numeric revision system (like Git)
                    return jsonify({
                        'revisions': [{'number': latest_revision, 'display': latest_revision}],
                        'latest_revision': latest_revision,
                        'total_count': 1
                    })

            except Exception as e:
                self.logger.error(f"Error getting revisions for repository {repo_id}: {e}")
                return jsonify({'error': f'Error getting revisions: {str(e)}'}), 500

        # User Management Routes
        @self.app.route('/users')
        def users_page():
            users = self.monitor_service.config.users
            repositories = self.monitor_service.config.repositories
            user_roles = [role.value for role in UserRole]
            return render_template('users.html', users=users, repositories=repositories, user_roles=user_roles)

        @self.app.route('/users/add', methods=['POST'])
        def add_user():
            try:
                success, message, user = self.user_service.create_user(
                    username=request.form.get('username', '').strip(),
                    email=request.form.get('email', '').strip(),
                    full_name=request.form.get('full_name', '').strip(),
                    role=UserRole(request.form.get('role', 'developer')),
                    phone=request.form.get('phone', '').strip() or None,
                    department=request.form.get('department', '').strip() or None,
                    receive_all_notifications=request.form.get('receive_all_notifications') == 'on'
                )

                if success:
                    self.config_manager.save_config(self.monitor_service.config)
                    flash(message, 'success')
                else:
                    flash(message, 'error')

            except Exception as e:
                flash(f"Error adding user: {str(e)}", 'error')

            return redirect(url_for('users_page'))

        @self.app.route('/users/update/<user_id>', methods=['POST'])
        def update_user(user_id):
            try:
                update_data = {
                    'username': request.form.get('username', '').strip(),
                    'email': request.form.get('email', '').strip(),
                    'full_name': request.form.get('full_name', '').strip(),
                    'role': request.form.get('role', 'developer'),
                    'phone': request.form.get('phone', '').strip() or None,
                    'department': request.form.get('department', '').strip() or None,
                    'enabled': request.form.get('enabled') == 'on',
                    'receive_all_notifications': request.form.get('receive_all_notifications') == 'on'
                }

                success, message = self.user_service.update_user(user_id, **update_data)

                if success:
                    self.config_manager.save_config(self.monitor_service.config)
                    flash(message, 'success')
                else:
                    flash(message, 'error')

            except Exception as e:
                flash(f"Error updating user: {str(e)}", 'error')

            return redirect(url_for('users_page'))

        @self.app.route('/users/delete/<user_id>', methods=['POST'])
        def delete_user(user_id):
            try:
                success, message = self.user_service.delete_user(user_id)

                if success:
                    self.config_manager.save_config(self.monitor_service.config)
                    flash(message, 'success')
                else:
                    flash(message, 'error')

            except Exception as e:
                flash(f"Error deleting user: {str(e)}", 'error')

            return redirect(url_for('users_page'))

        # Repository Discovery Routes
        @self.app.route('/repositories/discover')
        def repository_discovery_page():
            return render_template('repository_discovery.html',
                                 config=self.monitor_service.config)

        @self.app.route('/repositories/discover/scan', methods=['POST'])
        def scan_repositories():
            try:
                base_url = request.form.get('base_url', '').strip()
                username = request.form.get('username', '').strip() or None
                password = request.form.get('password', '').strip() or None
                max_depth = int(request.form.get('max_depth', 3))
                backend_type = request.form.get('backend_type', 'svn').strip()

                if not base_url:
                    return jsonify({'success': False, 'message': 'Base URL is required'})

                discovered_repos = self.backend_manager.discover_repositories(
                    backend_type, base_url, self.monitor_service.config, username, password, max_depth
                )

                # Convert RepositoryInfo objects to dictionaries for JSON response
                repo_dicts = []
                for repo_info in discovered_repos:
                    repo_dicts.append({
                        'name': repo_info.name,
                        'url': repo_info.url,
                        'path': repo_info.path,
                        'last_revision': repo_info.last_revision,
                        'last_author': repo_info.last_author,
                        'last_date': repo_info.last_date,
                        'size': repo_info.size,
                        'repository_type': repo_info.repository_type
                    })

                return jsonify({
                    'success': True,
                    'repositories': repo_dicts
                })

            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/repositories/import', methods=['POST'])
        def import_repository():
            try:
                repo_data = request.json
                if repo_data is None:
                    return jsonify({'success': False, 'message': 'Invalid JSON data'})

                username = repo_data.get('username')
                password = repo_data.get('password')
                product_doc_files = repo_data.get('product_documentation_files', [])

                # Create repository config from discovered data
                repo_config = RepositoryConfig(
                    name=repo_data['name'],
                    url=repo_data['url'],
                    username=username,
                    password=password,
                    last_revision=int(repo_data.get('last_revision', 0)) if repo_data.get('last_revision') else 0,
                    enabled=True,
                    product_documentation_files=product_doc_files
                )

                # Add to configuration
                self.monitor_service.config.repositories.append(repo_config)
                self.config_manager.save_config(self.monitor_service.config)

                return jsonify({
                    'success': True,
                    'message': f"Repository '{repo_config.name}' imported successfully",
                    'repository_id': repo_config.id
                })

            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/api/repositories/browse')
        def browse_repository_by_url():
            """Browse repository files by URL for import process"""
            try:
                url = request.args.get('url', '').strip()
                username = request.args.get('username', '').strip() or None
                password = request.args.get('password', '').strip() or None
                path = request.args.get('path', '/').strip()
                filter_docs = request.args.get('filter_docs', 'true').lower() == 'true'

                if not url:
                    return jsonify({'error': 'Repository URL is required'}), 400

                # Determine backend type from URL
                backend_type = 'svn'  # Default to SVN for now
                if 'git' in url.lower() or url.endswith('.git'):
                    backend_type = 'git'

                # Get the appropriate backend
                backend: Optional[RepositoryBackend] = self.backend_manager.get_backend(backend_type, self.monitor_service.config)
                if not backend:
                    return jsonify({'error': f'Unsupported repository type: {backend_type}'}), 400

                # Browse the repository at the specified path
                self.logger.info(f"Browsing repository: {url}, path: {path}, username: {username}")
                files = backend.browse_files(url, username, password, path)
                self.logger.info(f"Backend returned {len(files)} files")

                # Format the results for the frontend
                formatted_files = []
                for file_info in files:
                    self.logger.debug(f"File: {file_info}")
                    # Apply filtering based on filter_docs parameter
                    if filter_docs:
                        # Only include files that might be documentation or directories
                        if file_info['type'] == 'directory' or self._is_potential_documentation_file(file_info['name']):
                            formatted_files.append({
                                'name': file_info['name'],
                                'type': file_info['type'],
                                'path': file_info.get('path', f"{path.rstrip('/')}/{file_info['name']}"),
                                'size': file_info.get('size'),
                                'modified': file_info.get('modified')
                            })
                    else:
                        # Include all files and directories
                        formatted_files.append({
                            'name': file_info['name'],
                            'type': file_info['type'],
                            'path': file_info.get('path', f"{path.rstrip('/')}/{file_info['name']}"),
                            'size': file_info.get('size'),
                            'modified': file_info.get('modified')
                        })

                return jsonify({
                    'success': True,
                    'files': formatted_files,
                    'path': path,
                    'debug': {
                        'backend_type': backend_type,
                        'raw_files_count': len(files),
                        'formatted_files_count': len(formatted_files)
                    }
                })

            except Exception as e:
                self.logger.error(f"Error browsing repository: {e}")
                # Return more detailed error information
                error_msg = str(e)
                if "Authentication failed" in error_msg or "No more credentials" in error_msg:
                    error_msg = "Authentication failed. Please check your username and password."
                elif "Unable to connect" in error_msg:
                    error_msg = "Unable to connect to repository. Please check the URL and network connectivity."
                return jsonify({'error': error_msg, 'details': str(e)}), 500

        # Document Management Routes
        @self.app.route('/documents')
        def documents_page():
            """Display all generated documents with enhanced filtering, sorting, and organization"""
            # Get query parameters
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 25, type=int)
            repository_id = request.args.get('repository', None)
            code_review_filter = request.args.get('code_review', None)
            doc_impact_filter = request.args.get('doc_impact', None)

            # New enhanced filtering parameters
            risk_level_filter = request.args.get('risk_level', None)
            author_filter = request.args.get('author', None)
            date_from = request.args.get('date_from', None)
            date_to = request.args.get('date_to', None)
            search_query = request.args.get('search', None)

            # Sorting parameters
            sort_by = request.args.get('sort_by', 'date')  # date, repository, author, revision, size
            sort_order = request.args.get('sort_order', 'desc')  # asc, desc

            # View mode parameters
            view_mode = request.args.get('view_mode', 'table')  # table, cards, repository_groups

            # Convert string filters to boolean
            if code_review_filter == 'true':
                code_review_filter = True
            elif code_review_filter == 'false':
                code_review_filter = False
            else:
                code_review_filter = None

            if doc_impact_filter == 'true':
                doc_impact_filter = True
            elif doc_impact_filter == 'false':
                doc_impact_filter = False
            else:
                doc_impact_filter = None

            # Calculate offset
            offset = (page - 1) * per_page

            # Get documents with enhanced filtering and sorting
            documents = self.document_service.get_documents(
                limit=per_page,
                offset=offset,
                repository_id=repository_id,
                code_review_filter=code_review_filter,
                doc_impact_filter=doc_impact_filter,
                risk_level_filter=risk_level_filter,
                author_filter=author_filter,
                date_from=date_from,
                date_to=date_to,
                search_query=search_query,
                sort_by=sort_by,
                sort_order=sort_order
            )

            # Convert to legacy Document objects for template compatibility
            legacy_documents = [Document.from_record(doc) for doc in documents]

            # Get total count for pagination with enhanced filtering
            total_count = self.document_service.get_document_count(
                repository_id=repository_id,
                code_review_filter=code_review_filter,
                doc_impact_filter=doc_impact_filter,
                risk_level_filter=risk_level_filter,
                author_filter=author_filter,
                date_from=date_from,
                date_to=date_to,
                search_query=search_query
            )
            total_pages = (total_count + per_page - 1) // per_page

            # Calculate total size of all documents (not just current page)
            all_documents = self.document_service.get_documents(limit=10000)  # Get all documents
            total_size = sum(doc.size for doc in all_documents)

            # Get stats and processing info
            stats = self.document_service.get_repository_stats()
            processing_stats = self.document_service.get_processing_stats()

            # Get filter data for dropdowns
            available_repositories = self.document_service.get_available_repositories()
            available_authors = self.document_service.get_available_authors(repository_id)

            return render_template('documents.html',
                                 documents=legacy_documents,
                                 stats=stats,
                                 processing_stats=processing_stats,
                                 page=page,
                                 per_page=per_page,
                                 total_pages=total_pages,
                                 total_count=total_count,
                                 total_size=total_size,
                                 # Current filters
                                 repository_filter=repository_id,
                                 code_review_filter=code_review_filter,
                                 doc_impact_filter=doc_impact_filter,
                                 risk_level_filter=risk_level_filter,
                                 author_filter=author_filter,
                                 date_from=date_from,
                                 date_to=date_to,
                                 search_query=search_query,
                                 sort_by=sort_by,
                                 sort_order=sort_order,
                                 view_mode=view_mode,
                                 # Filter options
                                 available_repositories=available_repositories,
                                 available_authors=available_authors)

        @self.app.route('/documents/<doc_id>')
        def view_document(doc_id):
            """View a specific document"""
            from diff_service import DiffService

            document = self.document_service.get_document_by_id(doc_id)

            # If document not found, try alternative ID formats (for backward compatibility)
            original_doc_id = doc_id
            if not document:
                # Try adding "_revision_" if it's missing (e.g., "repo_11" -> "repo_revision_11")
                if '_revision_' not in doc_id and '_' in doc_id:
                    parts = doc_id.rsplit('_', 1)
                    if len(parts) == 2 and parts[1].isdigit():
                        alternative_id = f"{parts[0]}_revision_{parts[1]}"
                        self.logger.info(f"Trying alternative document ID format: {alternative_id}")
                        document = self.document_service.get_document_by_id(alternative_id)
                        if document:
                            self.logger.info(f"Found document with alternative ID: {alternative_id}")
                            doc_id = alternative_id  # Update doc_id for subsequent operations

                # Try removing "_revision_" if it exists (e.g., "repo_revision_11" -> "repo_11")
                elif '_revision_' in doc_id:
                    alternative_id = doc_id.replace('_revision_', '_')
                    self.logger.info(f"Trying alternative document ID format: {alternative_id}")
                    document = self.document_service.get_document_by_id(alternative_id)
                    if document:
                        self.logger.info(f"Found document with alternative ID: {alternative_id}")
                        doc_id = alternative_id  # Update doc_id for subsequent operations

            if not document:
                self.logger.warning(f"Document not found in database: {doc_id}")

                # Try to provide helpful suggestions by finding similar documents
                error_message = f'Document not found. The document with ID "{original_doc_id}" may have been deleted or moved.'

                # Extract repository ID and revision from the requested doc_id
                if '_' in original_doc_id:
                    parts = original_doc_id.split('_')
                    if len(parts) >= 2:
                        potential_repo_id = '_'.join(parts[:-1])
                        if 'revision' in parts:
                            # Remove 'revision' part to get clean repo ID
                            repo_parts = [p for p in parts if p != 'revision']
                            if len(repo_parts) >= 2:
                                potential_repo_id = '_'.join(repo_parts[:-1])

                        # Look for other documents from the same repository
                        try:
                            similar_docs = self.document_service.get_documents(repository_id=potential_repo_id, limit=5)
                            if similar_docs:
                                revisions = [str(doc.revision) for doc in similar_docs]
                                error_message += f' Available revisions for this repository: {", ".join(revisions)}.'
                        except Exception as e:
                            self.logger.debug(f"Error finding similar documents: {e}")

                flash(error_message, 'error')
                return redirect(url_for('documents_page'))

            # Check if document is orphaned (repository no longer exists)
            document_record = self.document_service.get_document_record_by_id(doc_id)
            if document_record:
                config = self.config_manager.load_config()
                valid_repo_ids = set(repo.id for repo in config.repositories)
                is_orphaned = document_record.repository_id not in valid_repo_ids

                if is_orphaned:
                    self.logger.warning(f"Attempted to view orphaned document: {doc_id} (repo: {document_record.repository_id})")
                    flash(f'Document unavailable. This document belongs to a repository that has been removed from the configuration. Repository ID: {document_record.repository_id}', 'warning')
                    return redirect(url_for('documents_page'))

            content = self.document_service.get_document_content(doc_id)
            if not content:
                # Get document record to check if file exists
                document_record = self.document_service.get_document_record_by_id(doc_id)
                if document_record:
                    import os
                    if not os.path.exists(document_record.filepath):
                        self.logger.error(f"Document file missing: {document_record.filepath}")
                        flash(f'Document file not found. The file "{document_record.filepath}" appears to have been moved or deleted from the filesystem.', 'error')
                    else:
                        self.logger.error(f"Error reading document content: {document_record.filepath}")
                        flash('Unable to read document content. The file may be corrupted or have permission issues.', 'error')
                else:
                    flash('Document content unavailable. Unable to locate document data.', 'error')
                return redirect(url_for('documents_page'))

            # Check if diff should be included
            include_diff = request.args.get('include_diff', 'false').lower() == 'true'
            diff_format = request.args.get('diff_format', 'unified')
            diff_content = None
            raw_diff_content = None

            if include_diff:
                # Get document record for diff generation
                document_record = self.document_service.get_document_record_by_id(doc_id)
                if document_record:
                    diff_service = DiffService(self.monitor_service.config_manager)
                    if diff_service.can_generate_diff(document_record):
                        # Get raw diff content first (unified format for copying)
                        raw_diff_content = diff_service.get_diff_for_document(document_record, 'unified')

                        # Get formatted diff content for display
                        if diff_format == 'side-by-side':
                            diff_content = diff_service.get_diff_for_document(document_record, 'side-by-side')
                        else:
                            diff_content = raw_diff_content

                        # Add diff to document content if available
                        if diff_content:
                            document.diff = diff_content

            # Don't load AI suggestions synchronously - they will be loaded via AJAX
            # This prevents the page from being sluggish on initial load
            ai_suggestions = None

            return render_template('document_view.html',
                                 document=document,
                                 content=content,
                                 include_diff=include_diff,
                                 diff_content=diff_content,
                                 raw_diff_content=raw_diff_content,
                                 diff_format=diff_format,
                                 ai_suggestions=ai_suggestions,
                                 config=self.monitor_service.config)

        @self.app.route('/api/documents')
        def api_documents():
            """API endpoint for documents list with pagination"""
            # Get query parameters
            limit = request.args.get('limit', 50, type=int)
            offset = request.args.get('offset', 0, type=int)
            # Support both 'repository' and 'repository_id' parameter names for flexibility
            repository_id = request.args.get('repository_id', None) or request.args.get('repository', None)
            code_review_filter = request.args.get('code_review', None)
            doc_impact_filter = request.args.get('doc_impact', None)

            # Convert string filters to boolean
            if code_review_filter == 'true':
                code_review_filter = True
            elif code_review_filter == 'false':
                code_review_filter = False
            else:
                code_review_filter = None

            if doc_impact_filter == 'true':
                doc_impact_filter = True
            elif doc_impact_filter == 'false':
                doc_impact_filter = False
            else:
                doc_impact_filter = None

            # Get documents
            documents = self.document_service.get_documents(
                limit=limit,
                offset=offset,
                repository_id=repository_id,
                code_review_filter=code_review_filter,
                doc_impact_filter=doc_impact_filter
            )

            # Get total count
            total_count = self.document_service.get_document_count(repository_id)

            return jsonify({
                'documents': [
                    {
                        'id': doc.id,
                        'repository_id': doc.repository_id,
                        'repository_name': doc.repository_name,
                        'revision': doc.revision,
                        'date': doc.date.isoformat(),
                        'author': doc.author,
                        'commit_message': doc.commit_message,
                        'filename': doc.filename,
                        'size': doc.size,
                        'code_review_recommended': doc.code_review_recommended,
                        'code_review_priority': doc.code_review_priority,
                        'documentation_impact': doc.documentation_impact,
                        'risk_level': doc.risk_level
                    }
                    for doc in documents
                ],
                'total_count': total_count,
                'stats': self.document_service.get_repository_stats(),
                'processing_stats': self.document_service.get_processing_stats()
            })

        @self.app.route('/api/documents/<doc_id>/delete', methods=['POST'])
        def api_delete_document(doc_id):
            """Delete a document, including orphaned documents"""
            try:
                self.logger.info(f"Attempting to delete document: {doc_id}")

                # First check if document exists
                document = self.document_service.get_document_record_by_id(doc_id)
                self.logger.info(f"Document lookup result: {document}")

                # If document not found, try alternative ID formats (for backward compatibility)
                if not document:
                    original_doc_id = doc_id
                    # Try adding "_revision_" if it's missing
                    if '_revision_' not in doc_id and '_' in doc_id:
                        parts = doc_id.rsplit('_', 1)
                        if len(parts) == 2 and parts[1].isdigit():
                            alternative_id = f"{parts[0]}_revision_{parts[1]}"
                            self.logger.info(f"Trying alternative document ID format: {alternative_id}")
                            document = self.document_service.get_document_record_by_id(alternative_id)
                            if document:
                                doc_id = alternative_id  # Update doc_id for subsequent operations

                    # Try removing "_revision_" if it exists
                    elif '_revision_' in doc_id:
                        alternative_id = doc_id.replace('_revision_', '_')
                        self.logger.info(f"Trying alternative document ID format: {alternative_id}")
                        document = self.document_service.get_document_record_by_id(alternative_id)
                        if document:
                            doc_id = alternative_id  # Update doc_id for subsequent operations

                if not document:
                    self.logger.warning(f"Document not found in database: {doc_id}")
                    return jsonify({
                        'success': False,
                        'message': f'Document not found. The document with ID "{doc_id}" may have been deleted or is no longer available.',
                        'error_code': 'DOCUMENT_NOT_FOUND'
                    }), 404

                # Check if document is orphaned (repository no longer exists)
                config = self.config_manager.load_config()
                valid_repo_ids = set(repo.id for repo in config.repositories)
                is_orphaned = document.repository_id not in valid_repo_ids

                if is_orphaned:
                    # For orphaned documents, just remove from database
                    success = self.document_service.delete_document(doc_id)
                    if success:
                        self.logger.info(f"Deleted orphaned document: {doc_id} (repo: {document.repository_id})")
                        return jsonify({'success': True, 'message': 'Orphaned document removed from database'})
                    else:
                        return jsonify({'success': False, 'message': 'Failed to delete orphaned document'}), 500
                else:
                    # For valid documents, use normal deletion process
                    success = self.document_service.delete_document(doc_id)
                    if success:
                        return jsonify({'success': True, 'message': 'Document deleted successfully'})
                    else:
                        return jsonify({'success': False, 'message': 'Failed to delete document'}), 500

            except Exception as e:
                self.logger.error(f"Error deleting document {doc_id}: {e}")
                return jsonify({'success': False, 'message': f'Error deleting document: {str(e)}'}), 500

        @self.app.route('/api/documents/cleanup-orphaned', methods=['POST'])
        def api_cleanup_orphaned_documents():
            """Remove all orphaned documents from database"""
            try:
                # Get valid repository IDs
                config = self.config_manager.load_config()
                valid_repo_ids = set(repo.id for repo in config.repositories)

                # Get all documents
                all_documents = self.document_service.get_documents()

                orphaned_count = 0
                for doc in all_documents:
                    if doc.repository_id not in valid_repo_ids:
                        success = self.document_service.delete_document(doc.id)
                        if success:
                            orphaned_count += 1
                            self.logger.info(f"Cleaned up orphaned document: {doc.id} (repo: {doc.repository_id})")
                        else:
                            self.logger.warning(f"Failed to delete orphaned document: {doc.id}")

                return jsonify({
                    'success': True,
                    'message': f'Cleaned up {orphaned_count} orphaned documents',
                    'orphaned_count': orphaned_count
                })
            except Exception as e:
                self.logger.error(f"Error cleaning up orphaned documents: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500

        @self.app.route('/api/documents/rescan', methods=['POST'])
        def api_rescan_documents():
            """Force rescan of all documents"""
            try:
                self.document_service.force_rescan()
                return jsonify({'success': True, 'message': 'Document rescan initiated'})
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/api/documents/clear-cache', methods=['POST'])
        def api_clear_document_cache():
            """Clear document cache"""
            try:
                self.document_service._invalidate_cache()
                return jsonify({'success': True, 'message': 'Document cache cleared'})
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/api/documents/delete-all', methods=['POST'])
        def api_delete_all_documents():
            """Delete ALL documents (database records and physical files)"""
            try:
                import shutil
                import os

                # Count existing files first
                repositories_dir = "/app/data/output/repositories"
                file_count = 0
                if os.path.exists(repositories_dir):
                    for root, dirs, files in os.walk(repositories_dir):
                        file_count += len([f for f in files if f.endswith('.md')])

                # Delete the entire repositories directory
                if os.path.exists(repositories_dir):
                    shutil.rmtree(repositories_dir)
                    self.logger.info(f"Deleted repositories directory: {repositories_dir}")

                # Clear all documents from database
                db_count = self.document_service.clear_all_documents()

                # Clear document cache
                self.document_service._invalidate_cache()

                return jsonify({
                    'success': True,
                    'message': f'Deleted all documents: {file_count} files and {db_count} database records',
                    'deleted_files': file_count,
                    'deleted_db_records': db_count
                })
            except Exception as e:
                self.logger.error(f"Error deleting all documents: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500

        @self.app.route('/api/database/reset', methods=['POST'])
        def api_reset_database():
            """Reset the entire database - recreate with fresh schema"""
            try:
                import os
                import shutil
                import time
                from document_database import DocumentDatabase

                # Backup the current database
                db_path = "/app/data/documents.db"
                backup_path = f"{db_path}.backup.{int(time.time())}"

                if os.path.exists(db_path):
                    shutil.copy2(db_path, backup_path)
                    self.logger.info(f"Created database backup: {backup_path}")

                # Delete the current database file
                if os.path.exists(db_path):
                    os.remove(db_path)
                    self.logger.info(f"Deleted database file: {db_path}")

                # Create a new database with fresh schema
                new_db = DocumentDatabase(db_path)
                self.logger.info(f"Created new database with fresh schema: {db_path}")

                # Also delete all document files
                repositories_dir = "/app/data/output/repositories"
                file_count = 0
                if os.path.exists(repositories_dir):
                    for root, dirs, files in os.walk(repositories_dir):
                        file_count += len([f for f in files if f.endswith('.md')])
                    shutil.rmtree(repositories_dir)
                    self.logger.info(f"Deleted {file_count} document files")

                # Clear document service cache
                self.document_service.force_rescan()

                return jsonify({
                    'success': True,
                    'message': f'Database reset successfully. Backup created: {os.path.basename(backup_path)}',
                    'backup_file': os.path.basename(backup_path),
                    'deleted_files': file_count
                })
            except Exception as e:
                self.logger.error(f"Error resetting database: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500

        # Historical Scanning Routes
        @self.app.route('/repositories/<repo_id>/historical-scan')
        def historical_scan_page(repo_id):
            """Display historical scanning configuration for a repository"""
            repo = None
            for r in self.monitor_service.config.repositories:
                if r.id == repo_id:
                    repo = r
                    break

            if not repo:
                flash('Repository not found', 'error')
                return redirect(url_for('repositories_page'))

            # Get scan progress if any
            scan_progress = self.historical_scanner.get_scan_progress(repo_id)

            return render_template('historical_scan.html',
                                 repository=repo,
                                 scan_progress=scan_progress,
                                 scan_statuses=HistoricalScanStatus)

        @self.app.route('/repositories/<repo_id>/historical-scan/configure', methods=['POST'])
        def configure_historical_scan(repo_id):
            """Configure historical scanning for a repository"""
            try:
                repo = None
                for r in self.monitor_service.config.repositories:
                    if r.id == repo_id:
                        repo = r
                        break

                if not repo:
                    return jsonify({'success': False, 'message': 'Repository not found'})

                # Parse form data
                scan_config = HistoricalScanConfig()
                scan_config.enabled = request.form.get('enabled') == 'on'
                scan_config.scan_by_revision = request.form.get('scan_by_revision') == 'on'
                scan_config.scan_by_date = request.form.get('scan_by_date') == 'on'

                # Revision range
                if scan_config.scan_by_revision:
                    start_rev = request.form.get('start_revision', '').strip()
                    end_rev = request.form.get('end_revision', '').strip()
                    scan_config.start_revision = int(start_rev) if start_rev else None
                    scan_config.end_revision = int(end_rev) if end_rev else None

                # Date range
                if scan_config.scan_by_date:
                    from datetime import datetime
                    start_date = request.form.get('start_date', '').strip()
                    end_date = request.form.get('end_date', '').strip()
                    if start_date:
                        scan_config.start_date = datetime.fromisoformat(start_date)
                    if end_date:
                        scan_config.end_date = datetime.fromisoformat(end_date)

                # Other settings
                scan_config.batch_size = int(request.form.get('batch_size', 10))
                scan_config.include_merge_commits = request.form.get('include_merge_commits') == 'on'
                scan_config.skip_large_commits = request.form.get('skip_large_commits') == 'on'
                scan_config.max_files_per_commit = int(request.form.get('max_files_per_commit', 100))

                # Analysis preferences
                scan_config.generate_documentation = request.form.get('generate_documentation') == 'on'
                scan_config.analyze_code_review = request.form.get('analyze_code_review') == 'on'
                scan_config.analyze_documentation_impact = request.form.get('analyze_documentation_impact') == 'on'

                # Update repository configuration
                repo.historical_scan = scan_config
                self.monitor_service.save_config()

                return jsonify({'success': True, 'message': 'Historical scan configuration saved'})

            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/repositories/<repo_id>/historical-scan/start', methods=['POST'])
        def start_historical_scan(repo_id):
            """Start historical scanning for a repository"""
            try:
                repo = None
                for r in self.monitor_service.config.repositories:
                    if r.id == repo_id:
                        repo = r
                        break

                if not repo:
                    return jsonify({'success': False, 'message': 'Repository not found'})

                # Always update configuration with form data if provided (auto-save on start)
                if request.form:
                    try:
                        # Parse form data and save configuration first
                        if not repo.historical_scan:
                            scan_config = HistoricalScanConfig()
                        else:
                            scan_config = repo.historical_scan

                        scan_config.enabled = request.form.get('enabled') == 'on'
                        scan_config.scan_by_revision = request.form.get('scan_by_revision') == 'on'
                        scan_config.scan_by_date = request.form.get('scan_by_date') == 'on'

                        # Revision range
                        if scan_config.scan_by_revision:
                            start_rev = request.form.get('start_revision', '').strip()
                            end_rev = request.form.get('end_revision', '').strip()
                            scan_config.start_revision = int(start_rev) if start_rev else None
                            scan_config.end_revision = int(end_rev) if end_rev else None

                            # Date range
                            if scan_config.scan_by_date:
                                from datetime import datetime
                                start_date = request.form.get('start_date', '').strip()
                                end_date = request.form.get('end_date', '').strip()
                                if start_date:
                                    scan_config.start_date = datetime.fromisoformat(start_date)
                                if end_date:
                                    scan_config.end_date = datetime.fromisoformat(end_date)

                            # Other settings
                            scan_config.batch_size = int(request.form.get('batch_size', 10))
                            scan_config.include_merge_commits = request.form.get('include_merge_commits') == 'on'
                            scan_config.skip_large_commits = request.form.get('skip_large_commits') == 'on'
                            scan_config.max_files_per_commit = int(request.form.get('max_files_per_commit', 100))

                            # Analysis preferences
                            scan_config.generate_documentation = request.form.get('generate_documentation') == 'on'
                            scan_config.analyze_code_review = request.form.get('analyze_code_review') == 'on'
                            scan_config.analyze_documentation_impact = request.form.get('analyze_documentation_impact') == 'on'

                            # Update repository configuration
                            repo.historical_scan = scan_config
                            self.monitor_service.save_config()

                            # Verify configuration is now enabled
                            if not scan_config.enabled:
                                return jsonify({'success': False, 'message': 'Historical scanning must be enabled to start scan'})

                    except Exception as e:
                        return jsonify({'success': False, 'message': f'Error auto-configuring scan: {str(e)}'})

                # Check if historical scanning is configured and enabled
                if not repo.historical_scan or not repo.historical_scan.enabled:
                    # Auto-configure with sensible defaults for newly imported repositories
                    try:
                        self.logger.info(f"Auto-configuring historical scan for {repo.name}")

                        # Get backend to determine revision range
                        backend = self.monitor_service.backend_manager.get_backend_for_repository(repo, None)
                        if not backend:
                            return jsonify({'success': False, 'message': 'No backend available for repository'})

                        # Get the latest revision
                        latest_revision = backend.get_latest_revision(repo)
                        if not latest_revision:
                            return jsonify({'success': False, 'message': 'Could not determine latest revision'})

                        # Create default scan configuration for last 10 revisions
                        latest_rev_int = int(latest_revision)
                        start_revision = max(1, latest_rev_int - 9)  # Last 10 revisions, but not below 1

                        scan_config = HistoricalScanConfig(
                            enabled=True,
                            scan_by_revision=True,
                            start_revision=start_revision,  # Start from 10 revisions ago
                            end_revision=latest_rev_int,    # Scan up to latest
                            batch_size=10,
                            include_merge_commits=True,
                            skip_large_commits=False,
                            max_files_per_commit=100
                        )

                        # Update repository configuration
                        repo.historical_scan = scan_config
                        self.monitor_service.save_config()

                        self.logger.info(f"Auto-configured historical scan for {repo.name}: revisions {start_revision}-{latest_rev_int} (last 10 revisions)")

                    except Exception as e:
                        self.logger.error(f"Error auto-configuring historical scan for {repo.name}: {e}")
                        return jsonify({'success': False, 'message': f'Could not auto-configure historical scanning: {str(e)}'})

                # Final check
                if not repo.historical_scan or not repo.historical_scan.enabled:
                    return jsonify({'success': False, 'message': 'Historical scanning could not be configured. Please configure manually first.'})

                # Queue the scan
                success = self.historical_scanner.queue_scan(repo, repo.historical_scan)

                if success:
                    return jsonify({'success': True, 'message': 'Historical scan started'})
                else:
                    # Check if there's a specific error message from the scanner
                    scan_progress = self.historical_scanner.get_scan_progress(repo.id)
                    if scan_progress and scan_progress.error_message:
                        return jsonify({'success': False, 'message': scan_progress.error_message})
                    else:
                        return jsonify({'success': False, 'message': 'Failed to start scan - please check your configuration'})

            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/repositories/<repo_id>/historical-scan/cancel', methods=['POST'])
        def cancel_historical_scan(repo_id):
            """Cancel historical scanning for a repository"""
            try:
                success = self.historical_scanner.cancel_scan(repo_id)

                if success:
                    return jsonify({'success': True, 'message': 'Historical scan cancelled'})
                else:
                    return jsonify({'success': False, 'message': 'No active scan to cancel'})

            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/repositories/<repo_id>/historical-scan/reset', methods=['POST'])
        def reset_historical_scan(repo_id):
            """Reset historical scan status for a repository"""
            try:
                # Find the repository
                repo = None
                for r in self.monitor_service.config.repositories:
                    if r.id == repo_id:
                        repo = r
                        break

                if not repo:
                    return jsonify({'success': False, 'message': 'Repository not found'})

                # Reset historical scan status
                if repo.historical_scan:
                    repo.historical_scan.last_scanned_revision = None
                    repo.historical_scan.scan_status = HistoricalScanStatus.NOT_STARTED
                    repo.historical_scan.scan_started_at = None
                    repo.historical_scan.scan_completed_at = None
                    repo.historical_scan.processed_revisions = 0
                    repo.historical_scan.failed_revisions = 0
                    repo.historical_scan.error_message = None

                    # Save configuration
                    self.monitor_service.save_config()

                    # Clear any completed scan progress from memory
                    if repo_id in self.historical_scanner.completed_scans:
                        del self.historical_scanner.completed_scans[repo_id]

                    self.logger.info(f"Reset historical scan status for repository {repo.name}")
                    return jsonify({'success': True, 'message': 'Historical scan status reset successfully'})
                else:
                    return jsonify({'success': False, 'message': 'No historical scan configuration found'})

            except Exception as e:
                self.logger.error(f"Error resetting historical scan for {repo_id}: {e}")
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/api/historical-scan/progress')
        def api_historical_scan_progress():
            """Get progress for all historical scans"""
            try:
                all_progress = self.historical_scanner.get_all_scan_progress()
                statistics = self.historical_scanner.get_statistics()

                # Convert progress objects to dictionaries
                progress_data = {}
                for repo_id, progress in all_progress.items():
                    progress_data[repo_id] = {
                        'repository_id': progress.repository_id,
                        'total_revisions': progress.total_revisions,
                        'processed_revisions': progress.processed_revisions,
                        'failed_revisions': progress.failed_revisions,
                        'current_revision': progress.current_revision,
                        'started_at': progress.started_at.isoformat() if progress.started_at else None,
                        'estimated_completion': progress.estimated_completion.isoformat() if progress.estimated_completion else None,
                        'error_message': progress.error_message,
                        'status': progress.status.value
                    }

                return jsonify({
                    'progress': progress_data,
                    'statistics': statistics
                })

            except Exception as e:
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/documents/processing-stats')
        def api_processing_stats():
            """Get document processing statistics"""
            return jsonify(self.document_service.get_processing_stats())

        @self.app.route('/api/documents/cache-stats')
        def api_cache_stats():
            """Get cache performance statistics"""
            return jsonify(self.document_service.get_cache_stats())

        @self.app.route('/api/documents/migration-status')
        def api_migration_status():
            """Get database migration status"""
            return jsonify(self.document_service.get_migration_status())

        @self.app.route('/api/documents/<doc_id>/diff')
        def api_get_document_diff(doc_id):
            """Get document diff content generated on-demand"""
            from diff_service import DiffService

            document = self.document_service.get_document_record_by_id(doc_id)
            if not document:
                self.logger.warning(f"Document not found for diff generation: {doc_id}")
                return jsonify({
                    'error': f'Document not found. Unable to generate diff for document ID "{doc_id}".',
                    'error_code': 'DOCUMENT_NOT_FOUND'
                }), 404

            # Get format parameter (unified or side-by-side)
            format_type = request.args.get('format', 'unified')
            if format_type not in ['unified', 'side-by-side']:
                return jsonify({'error': 'Invalid format. Use "unified" or "side-by-side"'}), 400

            # Generate diff on-demand using DiffService
            diff_service = DiffService(self.monitor_service.config_manager)

            if not diff_service.can_generate_diff(document):
                self.logger.warning(f"Cannot generate diff for document {doc_id} - missing repository metadata")
                return jsonify({
                    'error': 'Cannot generate diff for this document. The document may be missing repository metadata or the repository may no longer be accessible.',
                    'error_code': 'DIFF_GENERATION_FAILED'
                }), 404

            diff_content = diff_service.get_diff_for_document(document, format_type)
            if not diff_content:
                return jsonify({'error': 'Failed to generate diff content'}), 500

            return jsonify({'diff': diff_content, 'format': format_type})

        @self.app.route('/api/documents/<doc_id>/ai-suggestions')
        def api_get_ai_suggestions(doc_id):
            """Get AI documentation suggestions asynchronously"""
            try:
                document = self.document_service.get_document_by_id(doc_id)
                if not document:
                    self.logger.warning(f"Document not found for AI suggestions: {doc_id}")
                    return jsonify({
                        'error': f'Document not found. Unable to generate AI suggestions for document ID "{doc_id}".',
                        'error_code': 'DOCUMENT_NOT_FOUND'
                    }), 404

                # Generate AI suggestions (this may take time)
                ai_suggestions = self.document_service.get_ai_documentation_suggestions(doc_id)

                return jsonify({
                    'suggestions': ai_suggestions,
                    'success': True
                })
            except Exception as e:
                self.logger.error(f"Error getting AI suggestions for {doc_id}: {e}")
                return jsonify({
                    'error': 'Failed to generate AI suggestions',
                    'success': False
                }), 500

        @self.app.route('/api/documents/<doc_id>/download/pdf', methods=['POST'])
        def api_download_document_pdf(doc_id):
            """Generate and download document as PDF"""
            try:
                from pdf_generator import PDFGenerator

                document = self.document_service.get_document_by_id(doc_id)
                if not document:
                    self.logger.warning(f"Document not found for PDF generation: {doc_id}")
                    return jsonify({
                        'error': f'Document not found. Unable to generate PDF for document ID "{doc_id}".',
                        'error_code': 'DOCUMENT_NOT_FOUND'
                    }), 404

                # Get request data
                data = request.get_json()
                if not data:
                    return jsonify({'error': 'No data provided'}), 400

                content = data.get('content', '')
                diff_content = data.get('diff_content', '')
                document_info = data.get('document_info', {})

                # Generate PDF
                pdf_generator = PDFGenerator()
                pdf_bytes = pdf_generator.generate_document_pdf(
                    content=content,
                    diff_content=diff_content,
                    document_info=document_info,
                    _document=document
                )

                if not pdf_bytes:
                    return jsonify({'error': 'Failed to generate PDF'}), 500

                # Return PDF as response
                response = make_response(pdf_bytes)
                response.headers['Content-Type'] = 'application/pdf'
                response.headers['Content-Disposition'] = f'attachment; filename="{document.display_name.replace(" ", "_")}.pdf"'
                return response

            except ImportError:
                return jsonify({'error': 'PDF generation not available - missing dependencies'}), 500
            except Exception as e:
                self.logger.error(f"PDF generation failed: {e}")
                return jsonify({'error': 'PDF generation failed'}), 500

        @self.app.route('/api/documents/<doc_id>/feedback/code-review', methods=['POST'])
        def api_update_code_review_feedback(doc_id):
            """Update code review feedback for a document"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': 'No data provided'}), 400

                status = data.get('status')
                comments = data.get('comments')
                reviewer = data.get('reviewer')

                if not status:
                    return jsonify({'error': 'Status is required'}), 400

                if status not in ['approved', 'rejected', 'needs_changes', 'in_progress']:
                    return jsonify({'error': 'Invalid status. Use: approved, rejected, needs_changes, in_progress'}), 400

                success = self.document_service.update_code_review_feedback(doc_id, status, comments, reviewer)
                if success:
                    return jsonify({'success': True, 'message': 'Code review feedback updated'})
                else:
                    return jsonify({'error': 'Failed to update code review feedback'}), 500

            except Exception as e:
                self.logger.error(f"Error updating code review feedback: {e}")
                return jsonify({'error': 'Internal server error'}), 500

        @self.app.route('/api/documents/<doc_id>/feedback/documentation', methods=['POST'])
        def api_update_documentation_feedback(doc_id):
            """Update documentation quality feedback for a document"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': 'No data provided'}), 400

                rating = data.get('rating')
                comments = data.get('comments')
                updated_by = data.get('updated_by')

                if rating is not None:
                    try:
                        rating = int(rating)
                        if rating < 1 or rating > 5:
                            return jsonify({'error': 'Rating must be between 1 and 5'}), 400
                    except (ValueError, TypeError):
                        return jsonify({'error': 'Rating must be a number between 1 and 5'}), 400

                success = self.document_service.update_documentation_feedback(doc_id, rating, comments, updated_by)
                if success:
                    return jsonify({'success': True, 'message': 'Documentation feedback updated'})
                else:
                    return jsonify({'error': 'Failed to update documentation feedback'}), 500

            except Exception as e:
                self.logger.error(f"Error updating documentation feedback: {e}")
                return jsonify({'error': 'Internal server error'}), 500

        @self.app.route('/api/documents/<doc_id>/feedback/risk-assessment', methods=['POST'])
        def api_update_risk_assessment_feedback(doc_id):
            """Update risk assessment override for a document"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': 'No data provided'}), 400

                risk_override = data.get('risk_override')
                comments = data.get('comments')
                updated_by = data.get('updated_by')

                if not risk_override:
                    return jsonify({'error': 'Risk override is required'}), 400

                if risk_override not in ['HIGH', 'MEDIUM', 'LOW']:
                    return jsonify({'error': 'Invalid risk level. Use: HIGH, MEDIUM, LOW'}), 400

                success = self.document_service.update_risk_assessment_override(doc_id, risk_override, comments, updated_by)
                if success:
                    return jsonify({'success': True, 'message': 'Risk assessment override updated'})
                else:
                    return jsonify({'error': 'Failed to update risk assessment override'}), 500

            except Exception as e:
                self.logger.error(f"Error updating risk assessment override: {e}")
                return jsonify({'error': 'Internal server error'}), 500

        @self.app.route('/api/documents/<doc_id>/feedback/documentation-input', methods=['POST'])
        def api_update_documentation_input(doc_id):
            """Update user documentation input/augmentation for a document"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': 'No data provided'}), 400

                documentation_input = data.get('documentation_input')
                suggestions = data.get('suggestions')
                input_by = data.get('input_by')

                if not documentation_input and not suggestions:
                    return jsonify({'error': 'Either documentation input or suggestions is required'}), 400

                success = self.document_service.update_documentation_input(doc_id, documentation_input, suggestions, input_by)
                if success:
                    return jsonify({'success': True, 'message': 'Documentation input updated'})
                else:
                    return jsonify({'error': 'Failed to update documentation input'}), 500

            except Exception as e:
                self.logger.error(f"Error updating documentation input: {e}")
                return jsonify({'error': 'Internal server error'}), 500

        @self.app.route('/api/repositories/<repo_id>/browse')
        def browse_repository(repo_id):
            """Browse repository files for document selection"""
            try:
                path = request.args.get('path', '/')
                filter_docs = request.args.get('filter_docs', 'true').lower() == 'true'

                # Get repository configuration
                repo = self.monitor_service.config.get_repository_by_id(repo_id)
                if not repo:
                    return jsonify({'success': False, 'error': 'Repository not found'}), 404

                # Get the appropriate backend for this repository
                backend = self.backend_manager.get_backend_for_repository(repo, self.monitor_service.config)
                if not backend:
                    return jsonify({'success': False, 'error': 'Unsupported repository type'}), 400

                # Browse the repository at the specified path
                files = backend.browse_files(repo.url, repo.username, repo.password, path)

                # Filter and format the results for the frontend
                formatted_files = []
                for file_info in files:
                    # Apply filtering based on filter_docs parameter
                    if filter_docs:
                        # Only include files that might be documentation or directories
                        if file_info['type'] == 'directory' or self._is_potential_documentation_file(file_info['name']):
                            formatted_files.append({
                                'name': file_info['name'],
                                'type': file_info['type'],
                                'size': file_info.get('size'),
                                'modified': file_info.get('modified')
                            })
                    else:
                        # Include all files and directories
                        formatted_files.append({
                            'name': file_info['name'],
                            'type': file_info['type'],
                            'size': file_info.get('size'),
                            'modified': file_info.get('modified')
                        })

                return jsonify({
                    'success': True,
                    'files': formatted_files,
                    'path': path
                })

            except Exception as e:
                self.logger.error(f"Error browsing repository {repo_id}: {e}")
                return jsonify({
                    'success': False,
                    'error': f'Failed to browse repository: {str(e)}'
                }), 500

    def _is_potential_documentation_file(self, filename: str) -> bool:
        """Check if a file might be documentation based on its name and extension"""
        filename_lower = filename.lower()

        # Common documentation file extensions (including Microsoft Office formats)
        doc_extensions = [
            '.md', '.txt', '.rst', '.html', '.htm', '.pdf',
            '.doc', '.docx',  # Microsoft Word
            '.rtf',           # Rich Text Format
            '.odt',           # OpenDocument Text
            '.pages'          # Apple Pages
        ]
        if any(filename_lower.endswith(ext) for ext in doc_extensions):
            return True

        # Common documentation file names (without extension)
        doc_names = [
            'readme', 'changelog', 'changes', 'history', 'news', 'authors', 'contributors',
            'license', 'copying', 'install', 'installation', 'setup', 'usage', 'manual',
            'guide', 'tutorial', 'howto', 'faq', 'api', 'reference', 'documentation',
            'docs', 'features', 'roadmap', 'todo', 'notes', 'release'
        ]

        filename_base = filename_lower.split('.')[0]
        if filename_base in doc_names:
            return True

        # Files in common documentation directories
        if any(keyword in filename_lower for keyword in ['doc', 'guide', 'manual', 'help', 'wiki']):
            return True

        return False

    def run(self):
        """Run the web interface"""
        import os
        debug_mode = os.getenv('FLASK_DEBUG', '0') == '1' or os.getenv('SVN_MONITOR_ENV') == 'development'

        self.logger.info(f"Starting web interface on {self.monitor_service.config.web_host}:{self.monitor_service.config.web_port}")
        if debug_mode:
            self.logger.info("Debug mode enabled - templates and static files will auto-reload")

        self.app.run(
            host=self.monitor_service.config.web_host,
            port=self.monitor_service.config.web_port,
            debug=debug_mode,
            use_reloader=debug_mode,
            use_debugger=debug_mode
        )
