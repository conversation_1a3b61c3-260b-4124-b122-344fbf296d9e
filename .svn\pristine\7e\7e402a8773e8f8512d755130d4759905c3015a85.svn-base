services:
  reposense-ai:
    build:
      context: .
      dockerfile: Dockerfile
    image: reposense-ai:latest
    container_name: reposense-ai
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      # Essential volumes - always mounted
      - ./data:/app/data
      - ./logs:/app/logs
      # Development volumes - mount source code for hot reloads
      - .:/app
      # Exclude node_modules and other build artifacts to avoid conflicts
      - /app/node_modules
      - /app/.git
    environment:
      # Web interface settings (minimal environment)
      - REPOSENSE_AI_WEB_HOST=0.0.0.0
      - REPOSENSE_AI_WEB_PORT=5000
      # Optional deployment overrides (uncomment if needed):
      # - OLLAMA_BASE_URL=http://localhost:11434
      # - OLLAMA_MODEL=qwen3
    networks:
      - reposense-ai-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "sundc:***********"
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

# Network
networks:
  reposense-ai-network:
    driver: bridge
