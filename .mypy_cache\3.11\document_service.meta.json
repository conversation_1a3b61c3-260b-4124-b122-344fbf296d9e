{"data_mtime": 1754859995, "dep_lines": [8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 332, 598, 634, 1075, 2066, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1120, 1142, 1089, 1100, 1141], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20, 20, 20], "dependencies": ["os", "re", "time", "logging", "requests", "pathlib", "typing", "dataclasses", "datetime", "document_database", "document_processor", "cache_manager", "ollama_client", "shutil", "prompt_templates", "json", "docx", "math", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "docx.document", "docx.shared", "docx.text", "docx.text.paragraph", "enum", "http", "http.cookiejar", "io", "json.decoder", "models", "requests.auth", "requests.models", "types", "typing_extensions"], "hash": "382b16d9ec0b2229ff91d78f5efe408126917b55", "id": "document_service", "ignore_all": false, "interface_hash": "4a65f54ca3eae5d25786c608c167d9fa354bc449", "mtime": 1754860052, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\document_service.py", "plugin_data": null, "size": 107544, "suppressed": ["striprtf.striprtf", "odf.opendocument", "textract", "docx2txt", "odf"], "version_id": "1.15.0"}