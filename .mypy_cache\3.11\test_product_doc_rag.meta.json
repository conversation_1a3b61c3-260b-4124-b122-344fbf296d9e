{"data_mtime": 1754871329, "dep_lines": [7, 8, 9, 10, 15, 16, 17, 296, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["sys", "logging", "pathlib", "datetime", "prompt_templates", "document_database", "models", "traceback", "builtins", "_frozen_importlib", "_typeshed", "abc", "os", "typing", "typing_extensions"], "hash": "fefa13f06fc09406583d7b897759a060bf68d09b", "id": "test_product_doc_rag", "ignore_all": false, "interface_hash": "982266cbe56c4d9e532f211fe939c495a64c5a2e", "mtime": 1754871328, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\test_product_doc_rag.py", "plugin_data": null, "size": 11195, "suppressed": [], "version_id": "1.15.0"}