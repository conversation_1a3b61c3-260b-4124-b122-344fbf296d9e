{% extends "base.html" %}

{% block title %}Historical Scan - {{ repository.name }} - RepoSense AI{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">Historical Scan</h1>
    <p class="page-subtitle">Configure and monitor historical scanning for {{ repository.name }}</p>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- Configuration Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Scan Configuration</h5>
            </div>
            <div class="card-body">
                <form id="historical-scan-form">
                    <!-- Enable/Disable -->
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="enabled" name="enabled" 
                               {% if repository.historical_scan and repository.historical_scan.enabled %}checked{% endif %}>
                        <label class="form-check-label" for="enabled">
                            Enable Historical Scanning
                        </label>
                    </div>

                    <!-- Scan Method -->
                    <div class="mb-3">
                        <label class="form-label">Scan Method</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="scan_method" id="scan_by_revision" value="revision" 
                                   {% if not repository.historical_scan or repository.historical_scan.scan_by_revision %}checked{% endif %}>
                            <label class="form-check-label" for="scan_by_revision">
                                Scan by Revision Range
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="scan_method" id="scan_by_date" value="date"
                                   {% if repository.historical_scan and repository.historical_scan.scan_by_date %}checked{% endif %}>
                            <label class="form-check-label" for="scan_by_date">
                                Scan by Date Range
                            </label>
                        </div>
                    </div>

                    <!-- Revision Range -->
                    <div id="revision-range-section" class="mb-3">
                        <label class="form-label">Revision Range</label>

                        <!-- Loading indicator -->
                        <div id="revision-loading" class="text-center py-2" style="display: none;">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading revisions...</span>
                            </div>
                            <span class="ms-2">Loading available revisions...</span>
                        </div>

                        <!-- Revision selector -->
                        <div id="revision-selector" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="start_revision" class="form-label">Start Revision</label>
                                    <select class="form-select" id="start_revision" name="start_revision">
                                        <option value="">Select start revision...</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="end_revision" class="form-label">End Revision</label>
                                    <select class="form-select" id="end_revision" name="end_revision">
                                        <option value="">Select end revision...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <small class="form-text text-muted">
                                        <span id="revision-info"></span>
                                    </small>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <div class="btn-group-sm" role="group" aria-label="Quick revision selection">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="select-all-revisions" title="Select all available revisions">
                                            <i class="fas fa-list"></i> All
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="select-recent-revisions" title="Select last 10 revisions">
                                            <i class="fas fa-clock"></i> Last 10
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="select-last-25" title="Select last 25 revisions">
                                            <i class="fas fa-history"></i> Last 25
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="select-last-50" title="Select last 50 revisions">
                                            <i class="fas fa-calendar-week"></i> Last 50
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="select-last-100" title="Select last 100 revisions">
                                            <i class="fas fa-calendar-alt"></i> Last 100
                                        </button>
                                    </div>
                                    <small class="form-text text-muted d-block mt-1">
                                        Quick shortcuts - automatically fills the range fields above
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Fallback manual input -->
                        <div id="revision-manual" class="row">
                            <div class="col-md-6">
                                <input type="number" class="form-control" id="start_revision_manual" name="start_revision_manual"
                                       placeholder="Start revision (optional)"
                                       value="{% if repository.historical_scan and repository.historical_scan.start_revision %}{{ repository.historical_scan.start_revision }}{% endif %}">
                            </div>
                            <div class="col-md-6">
                                <input type="number" class="form-control" id="end_revision_manual" name="end_revision_manual"
                                       placeholder="End revision (optional)"
                                       value="{% if repository.historical_scan and repository.historical_scan.end_revision %}{{ repository.historical_scan.end_revision }}{% endif %}">
                            </div>
                        </div>

                        <!-- Manual input controls -->
                        <div class="row mt-2">
                            <div class="col-12">
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="manual-clear" title="Clear revision fields">
                                    <i class="fas fa-eraser"></i> Clear Fields
                                </button>
                                <small class="form-text text-muted d-inline-block ms-3">
                                    Leave empty to scan from beginning/to latest
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Date Range -->
                    <div id="date-range-section" class="mb-3" style="display: none;">
                        <label class="form-label">Date Range</label>
                        <div class="row">
                            <div class="col-md-6">
                                <input type="date" class="form-control" id="start_date" name="start_date"
                                       value="{% if repository.historical_scan and repository.historical_scan.start_date %}{{ repository.historical_scan.start_date.strftime('%Y-%m-%d') }}{% endif %}">
                                <small class="form-text text-muted">Start date</small>
                            </div>
                            <div class="col-md-6">
                                <input type="date" class="form-control" id="end_date" name="end_date"
                                       value="{% if repository.historical_scan and repository.historical_scan.end_date %}{{ repository.historical_scan.end_date.strftime('%Y-%m-%d') }}{% endif %}">
                                <small class="form-text text-muted">End date</small>
                            </div>
                        </div>
                    </div>

                    <!-- Processing Options -->
                    <div class="mb-3">
                        <label class="form-label">Processing Options</label>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="batch_size" class="form-label">Batch Size</label>
                                <input type="number" class="form-control" id="batch_size" name="batch_size" 
                                       value="{% if repository.historical_scan %}{{ repository.historical_scan.batch_size }}{% else %}10{% endif %}" 
                                       min="1" max="100">
                                <small class="form-text text-muted">Revisions per batch</small>
                            </div>
                            <div class="col-md-6">
                                <label for="max_files_per_commit" class="form-label">Max Files per Commit</label>
                                <input type="number" class="form-control" id="max_files_per_commit" name="max_files_per_commit" 
                                       value="{% if repository.historical_scan %}{{ repository.historical_scan.max_files_per_commit }}{% else %}100{% endif %}" 
                                       min="1" max="1000">
                                <small class="form-text text-muted">Skip commits with more files</small>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Options -->
                    <div class="mb-3">
                        <label class="form-label">Advanced Options</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_merge_commits" name="include_merge_commits" 
                                   {% if not repository.historical_scan or repository.historical_scan.include_merge_commits %}checked{% endif %}>
                            <label class="form-check-label" for="include_merge_commits">
                                Include merge commits
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="skip_large_commits" name="skip_large_commits"
                                   {% if repository.historical_scan and repository.historical_scan.skip_large_commits %}checked{% endif %}>
                            <label class="form-check-label" for="skip_large_commits">
                                Skip large commits (based on max files setting)
                            </label>
                        </div>
                    </div>

                    <!-- Analysis Options -->
                    <div class="mb-3">
                        <label class="form-label">Analysis Options</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="generate_documentation" name="generate_documentation" 
                                   {% if not repository.historical_scan or repository.historical_scan.generate_documentation %}checked{% endif %}>
                            <label class="form-check-label" for="generate_documentation">
                                Generate documentation
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="analyze_code_review" name="analyze_code_review" 
                                   {% if not repository.historical_scan or repository.historical_scan.analyze_code_review %}checked{% endif %}>
                            <label class="form-check-label" for="analyze_code_review">
                                Analyze code review needs
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="analyze_documentation_impact" name="analyze_documentation_impact" 
                                   {% if not repository.historical_scan or repository.historical_scan.analyze_documentation_impact %}checked{% endif %}>
                            <label class="form-check-label" for="analyze_documentation_impact">
                                Analyze documentation impact
                            </label>
                        </div>
                    </div>

                    <!-- Error Display Area -->
                    <div id="scan-error-alert" class="alert alert-danger" style="display: none;" role="alert">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <div>
                                <strong>Scan Error:</strong>
                                <span id="scan-error-message"></span>
                            </div>
                            <button type="button" class="btn-close ms-auto" onclick="document.getElementById('scan-error-alert').style.display='none'"></button>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">Save Configuration</button>
                        <button type="button" id="start-scan-btn" class="btn btn-success"
                                {% if not repository.historical_scan or not repository.historical_scan.enabled %}disabled{% endif %}>
                            Start Scan
                        </button>
                        <button type="button" id="cancel-scan-btn" class="btn btn-danger" style="display: none;">
                            Cancel Scan
                        </button>
                        <button type="button" id="reset-scan-btn" class="btn btn-warning"
                                {% if not repository.historical_scan or (repository.historical_scan.scan_status.value == 'not_started' and not repository.historical_scan.last_scanned_revision) %}style="display: none;"{% endif %}>
                            <i class="fas fa-undo"></i> Reset Status
                        </button>
                        <a href="{{ url_for('repositories_page') }}" class="btn btn-secondary">Back to Repositories</a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Progress Panel -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Scan Progress</h5>
                <div class="d-flex align-items-center">
                    <div id="progress-indicator" class="text-muted me-2" style="display: none;">
                        <i class="fas fa-sync-alt fa-spin"></i>
                        <small>Updating...</small>
                    </div>
                    <button id="refresh-progress-btn" class="btn btn-outline-light btn-sm" onclick="updateProgress()" title="Refresh progress">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="progress-section">
                    {% if scan_progress %}
                        <div class="mb-3">
                            <strong>Status:</strong> 
                            <span class="badge bg-{% if scan_progress.status.value == 'completed' %}success{% elif scan_progress.status.value == 'failed' %}danger{% elif scan_progress.status.value == 'cancelled' %}warning{% else %}primary{% endif %}">
                                {{ scan_progress.status.value.title() }}
                            </span>
                        </div>
                        
                        {% if scan_progress.total_revisions > 0 %}
                        <div class="mb-3">
                            <strong>Progress:</strong> {{ scan_progress.processed_revisions }} / {{ scan_progress.total_revisions }}
                            <div class="progress mt-1">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: {{ (scan_progress.processed_revisions / scan_progress.total_revisions * 100) | round(1) }}%">
                                    {{ (scan_progress.processed_revisions / scan_progress.total_revisions * 100) | round(1) }}%
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if scan_progress.current_revision %}
                        <div class="mb-3">
                            <strong>Current Revision:</strong> {{ scan_progress.current_revision }}
                        </div>
                        {% endif %}
                        
                        {% if scan_progress.failed_revisions > 0 %}
                        <div class="mb-3">
                            <strong>Failed:</strong> {{ scan_progress.failed_revisions }}
                        </div>
                        {% endif %}
                        
                        {% if scan_progress.started_at %}
                        <div class="mb-3">
                            <strong>Started:</strong> {{ scan_progress.started_at.strftime('%Y-%m-%d %H:%M:%S') }}
                        </div>
                        {% endif %}
                        
                        {% if scan_progress.estimated_completion %}
                        <div class="mb-3">
                            <strong>Estimated Completion:</strong> {{ scan_progress.estimated_completion.strftime('%Y-%m-%d %H:%M:%S') }}
                        </div>
                        {% endif %}
                        
                        {% if scan_progress.error_message %}
                        <div class="alert alert-danger">
                            <strong>Error:</strong> {{ scan_progress.error_message }}
                        </div>
                        {% endif %}
                    {% else %}
                        <p class="text-muted">No scan in progress</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Repository Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title">Repository Info</h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ repository.name }}</p>
                <p><strong>URL:</strong> {{ repository.url }}</p>
                <p><strong>Last Revision:</strong> {{ repository.last_revision }}</p>
                <p><strong>Enabled:</strong> 
                    <span class="badge bg-{% if repository.enabled %}success{% else %}secondary{% endif %}">
                        {% if repository.enabled %}Yes{% else %}No{% endif %}
                    </span>
                </p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('historical-scan-form');
    const scanMethodRadios = document.querySelectorAll('input[name="scan_method"]');
    const revisionSection = document.getElementById('revision-range-section');
    const dateSection = document.getElementById('date-range-section');
    const startScanBtn = document.getElementById('start-scan-btn');
    const cancelScanBtn = document.getElementById('cancel-scan-btn');
    const enabledCheckbox = document.getElementById('enabled');

    // Revision selector elements
    const revisionLoading = document.getElementById('revision-loading');
    const revisionSelector = document.getElementById('revision-selector');
    const revisionManual = document.getElementById('revision-manual');
    const startRevisionSelect = document.getElementById('start_revision');
    const endRevisionSelect = document.getElementById('end_revision');
    const revisionInfo = document.getElementById('revision-info');
    const selectAllBtn = document.getElementById('select-all-revisions');
    const selectRecentBtn = document.getElementById('select-recent-revisions');
    const selectLast25Btn = document.getElementById('select-last-25');
    const selectLast50Btn = document.getElementById('select-last-50');
    const selectLast100Btn = document.getElementById('select-last-100');

    // Manual input elements
    const manualClearBtn = document.getElementById('manual-clear');
    const startRevisionManual = document.getElementById('start_revision_manual');
    const endRevisionManual = document.getElementById('end_revision_manual');

    let availableRevisions = [];

    // Load available revisions from the repository
    function loadRevisions() {
        revisionLoading.style.display = 'block';
        revisionSelector.style.display = 'none';
        revisionManual.style.display = 'none';

        fetch(`/api/repositories/{{ repository.id }}/revisions`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.error('Error loading revisions:', data.error);
                    showManualInput();
                    return;
                }

                availableRevisions = data.revisions;
                populateRevisionSelectors(data);
                showRevisionSelector();

            })
            .catch(error => {
                console.error('Error loading revisions:', error);
                showManualInput();
            });
    }

    function populateRevisionSelectors(data) {
        // Clear existing options
        startRevisionSelect.innerHTML = '<option value="">Select start revision...</option>';
        endRevisionSelect.innerHTML = '<option value="">Select end revision...</option>';

        // Add revision options (reverse order for start, normal order for end)
        const reversedRevisions = [...data.revisions].reverse();

        // Populate start revision (newest first)
        reversedRevisions.forEach(rev => {
            const option = document.createElement('option');
            option.value = rev.number;
            option.textContent = rev.display;
            startRevisionSelect.appendChild(option);
        });

        // Populate end revision (newest first for easier selection)
        reversedRevisions.forEach(rev => {
            const option = document.createElement('option');
            option.value = rev.number;
            option.textContent = rev.display;
            endRevisionSelect.appendChild(option);
        });

        // Update info text
        revisionInfo.textContent = `Repository has ${data.total_count} revisions (latest: r${data.latest_revision})`;
    }

    function showRevisionSelector() {
        revisionLoading.style.display = 'none';
        revisionSelector.style.display = 'block';
        revisionManual.style.display = 'none';
    }

    function showManualInput() {
        revisionLoading.style.display = 'none';
        revisionSelector.style.display = 'none';
        revisionManual.style.display = 'block';
    }

    // Toggle sections based on scan method
    function toggleSections() {
        const selectedMethod = document.querySelector('input[name="scan_method"]:checked').value;
        if (selectedMethod === 'revision') {
            revisionSection.style.display = 'block';
            dateSection.style.display = 'none';
            // Load revisions when switching to revision mode
            if (availableRevisions.length === 0) {
                loadRevisions();
            }
        } else {
            revisionSection.style.display = 'none';
            dateSection.style.display = 'block';
        }
    }

    // Helper button functions
    function selectAllRevisions() {
        if (availableRevisions.length > 0) {
            startRevisionSelect.value = availableRevisions[0].number; // First (oldest)
            endRevisionSelect.value = availableRevisions[availableRevisions.length - 1].number; // Last (newest)

            // Update the info display
            updateRevisionInfo();

            // Visual feedback
            const buttons = [selectAllBtn, selectRecentBtn, selectLast25Btn, selectLast50Btn, selectLast100Btn];
            buttons.forEach(btn => btn.classList.remove('btn-primary'));
            buttons.forEach(btn => btn.classList.add('btn-outline-secondary'));

            selectAllBtn.classList.remove('btn-outline-secondary');
            selectAllBtn.classList.add('btn-primary');
        }
    }

    function selectRecentRevisions() {
        selectLastNRevisions(10);
    }

    function selectLast25Revisions() {
        selectLastNRevisions(25);
    }

    function selectLast50Revisions() {
        selectLastNRevisions(50);
    }

    function selectLast100Revisions() {
        selectLastNRevisions(100);
    }

    function selectLastNRevisions(count) {
        // Check if we're in dropdown mode or manual input mode
        if (revisionSelector.style.display !== 'none' && availableRevisions.length > 0) {
            // Dropdown mode - use available revisions
            const recentCount = Math.min(count, availableRevisions.length);
            const startIndex = Math.max(0, availableRevisions.length - recentCount);
            startRevisionSelect.value = availableRevisions[startIndex].number;
            endRevisionSelect.value = availableRevisions[availableRevisions.length - 1].number;

            // Update the info display
            updateRevisionInfo();
        } else {
            // Manual input mode - estimate revision range
            setManualRevisionRange(count);
        }

        // Visual feedback
        highlightSelectedRange(count);
    }

    function highlightSelectedRange(count) {
        // Add visual feedback to show which range was selected
        const buttons = [selectAllBtn, selectRecentBtn, selectLast25Btn, selectLast50Btn, selectLast100Btn];
        buttons.forEach(btn => btn.classList.remove('btn-primary'));
        buttons.forEach(btn => btn.classList.add('btn-outline-secondary'));

        // Highlight the active button
        if (count === 10) {
            selectRecentBtn.classList.remove('btn-outline-secondary');
            selectRecentBtn.classList.add('btn-primary');
        } else if (count === 25) {
            selectLast25Btn.classList.remove('btn-outline-secondary');
            selectLast25Btn.classList.add('btn-primary');
        } else if (count === 50) {
            selectLast50Btn.classList.remove('btn-outline-secondary');
            selectLast50Btn.classList.add('btn-primary');
        } else if (count === 100) {
            selectLast100Btn.classList.remove('btn-outline-secondary');
            selectLast100Btn.classList.add('btn-primary');
        }
    }

    function updateRevisionInfo() {
        const startRev = startRevisionSelect.value;
        const endRev = endRevisionSelect.value;

        if (startRev && endRev) {
            const count = Math.abs(parseInt(endRev) - parseInt(startRev)) + 1;
            revisionInfo.textContent = `Selected ${count} revision${count !== 1 ? 's' : ''} (${startRev} to ${endRev})`;
        } else {
            revisionInfo.textContent = '';
        }
    }

    // Get revision values from either selector or manual input
    function getRevisionValues() {
        if (revisionSelector.style.display !== 'none') {
            // Use selector values
            return {
                start_revision: startRevisionSelect.value,
                end_revision: endRevisionSelect.value
            };
        } else {
            // Use manual input values
            return {
                start_revision: document.getElementById('start_revision_manual').value,
                end_revision: document.getElementById('end_revision_manual').value
            };
        }
    }

    // Update button states
    function updateButtonStates() {
        const enabled = enabledCheckbox.checked;
        startScanBtn.disabled = !enabled;
    }

    // Event listeners
    scanMethodRadios.forEach(radio => {
        radio.addEventListener('change', toggleSections);
    });

    enabledCheckbox.addEventListener('change', updateButtonStates);

    // Helper button listeners
    selectAllBtn.addEventListener('click', selectAllRevisions);
    selectRecentBtn.addEventListener('click', selectRecentRevisions);
    selectLast25Btn.addEventListener('click', selectLast25Revisions);
    selectLast50Btn.addEventListener('click', selectLast50Revisions);
    selectLast100Btn.addEventListener('click', selectLast100Revisions);

    // Update info when dropdowns change manually
    startRevisionSelect.addEventListener('change', function() {
        updateRevisionInfo();
        clearButtonHighlights();
    });

    endRevisionSelect.addEventListener('change', function() {
        updateRevisionInfo();
        clearButtonHighlights();
    });

    // Clear button listener
    if (manualClearBtn) {
        manualClearBtn.addEventListener('click', () => {
            // Clear both dropdown and manual inputs
            if (startRevisionSelect) startRevisionSelect.value = '';
            if (endRevisionSelect) endRevisionSelect.value = '';
            if (startRevisionManual) startRevisionManual.value = '';
            if (endRevisionManual) endRevisionManual.value = '';

            // Clear button highlights
            clearButtonHighlights();

            // Update info display
            if (revisionSelector.style.display !== 'none') {
                updateRevisionInfo();
            }
        });
    }

    // Clear highlights when inputs change manually
    if (startRevisionManual) {
        startRevisionManual.addEventListener('input', clearButtonHighlights);
    }
    if (endRevisionManual) {
        endRevisionManual.addEventListener('input', clearButtonHighlights);
    }

    function clearButtonHighlights() {
        const buttons = [selectAllBtn, selectRecentBtn, selectLast25Btn, selectLast50Btn, selectLast100Btn];
        buttons.forEach(btn => {
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-outline-secondary');
        });
    }

    // Manual input shortcut functions
    function setManualRevisionRange(count) {
        // Estimate latest revision or use a reasonable default
        const estimatedLatest = getEstimatedLatestRevision();

        const startRevision = Math.max(1, estimatedLatest - count + 1);
        const endRevision = estimatedLatest;

        startRevisionManual.value = startRevision;
        endRevisionManual.value = endRevision;
    }

    function getEstimatedLatestRevision() {
        // Try to get from current values, or use a reasonable default
        const currentEnd = parseInt(endRevisionManual.value);
        const currentStart = parseInt(startRevisionManual.value);

        if (currentEnd && currentEnd > 0) {
            return currentEnd;
        }

        if (currentStart && currentStart > 0) {
            return Math.max(currentStart + 50, 100); // Estimate based on start
        }

        // Default assumption for a typical repository
        return 300;
    }



    // Initialize
    toggleSections();
    updateButtonStates();

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(form);

        // Get revision values from appropriate input method
        const revisionValues = getRevisionValues();
        formData.set('start_revision', revisionValues.start_revision);
        formData.set('end_revision', revisionValues.end_revision);

        // Convert radio button to checkboxes for backend
        const scanMethod = formData.get('scan_method');
        formData.delete('scan_method');
        formData.set('scan_by_revision', scanMethod === 'revision' ? 'on' : '');
        formData.set('scan_by_date', scanMethod === 'date' ? 'on' : '');

        fetch(`/repositories/{{ repository.id }}/historical-scan/configure`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Configuration saved successfully!');
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error saving configuration: ' + error);
        });
    });

    // Start scan
    startScanBtn.addEventListener('click', function() {
        if (confirm('Start historical scan? This may take a while depending on the number of revisions.')) {
            // Prepare form data to auto-save configuration if needed
            const formData = new FormData(form);

            // Get revision values from appropriate input method
            const revisionValues = getRevisionValues();
            formData.set('start_revision', revisionValues.start_revision);
            formData.set('end_revision', revisionValues.end_revision);

            // Convert radio button to checkboxes for backend
            const scanMethod = formData.get('scan_method');
            formData.delete('scan_method');
            formData.set('scan_by_revision', scanMethod === 'revision' ? 'on' : '');
            formData.set('scan_by_date', scanMethod === 'date' ? 'on' : '');

            fetch(`/repositories/{{ repository.id }}/historical-scan/start`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Hide any previous error
                    document.getElementById('scan-error-alert').style.display = 'none';
                    alert('Historical scan started!');
                    // Start progress polling
                    if (progressUpdateInterval) {
                        clearInterval(progressUpdateInterval);
                    }
                    progressUpdateInterval = setInterval(updateProgress, 1000);
                    location.reload();
                } else {
                    // Show detailed error message
                    showScanError(data.message);
                }
            })
            .catch(error => {
                showScanError('Network error: ' + error);
            });
        }
    });

    // Cancel scan
    cancelScanBtn.addEventListener('click', function() {
        if (confirm('Cancel the current scan?')) {
            fetch(`/repositories/{{ repository.id }}/historical-scan/cancel`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Scan cancelled!');
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error cancelling scan: ' + error);
            });
        }
    });

    // Reset scan status
    const resetScanBtn = document.getElementById('reset-scan-btn');
    if (resetScanBtn) {
        resetScanBtn.addEventListener('click', function() {
            if (confirm('Reset the historical scan status? This will allow you to re-scan previously processed revisions.')) {
                fetch(`/repositories/{{ repository.id }}/historical-scan/reset`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Scan status reset successfully!');
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Error resetting scan status: ' + error);
                });
            }
        });
    }

    // Real-time progress updates
    let progressUpdateInterval = null;

    function updateProgress() {
        const indicator = document.getElementById('progress-indicator');
        indicator.style.display = 'block';

        fetch('/api/historical-scan/progress')
            .then(response => response.json())
            .then(data => {
                const repoProgress = data.progress['{{ repository.id }}'];
                if (repoProgress) {
                    updateProgressDisplay(repoProgress);

                    // Stop polling if scan is complete
                    if (repoProgress.status === 'completed' || repoProgress.status === 'failed' || repoProgress.status === 'cancelled') {
                        if (progressUpdateInterval) {
                            clearInterval(progressUpdateInterval);
                            progressUpdateInterval = null;
                        }

                        // Force final progress update for completed scans
                        if (repoProgress.status === 'completed') {
                            setTimeout(() => {
                                const progressBar = document.querySelector('#progress-section .progress-bar');
                                if (progressBar) {
                                    progressBar.style.width = '100%';
                                    progressBar.textContent = '100%';
                                    progressBar.classList.add('bg-success');
                                    console.log('Final progress bar update: 100%');
                                }
                            }, 200);
                        }
                    }
                } else {
                    // No progress data - check if we should stop polling
                    const progressSection = document.getElementById('progress-section');
                    if (progressSection.textContent.includes('No scan in progress')) {
                        if (progressUpdateInterval) {
                            clearInterval(progressUpdateInterval);
                            progressUpdateInterval = null;
                        }
                    }
                }
                indicator.style.display = 'none';
            })
            .catch(error => {
                console.error('Error fetching progress:', error);
                indicator.style.display = 'none';
            });
    }

    function updateProgressDisplay(progress) {
        const progressSection = document.getElementById('progress-section');

        // Add a subtle flash effect to show updates
        progressSection.style.transition = 'background-color 0.3s';
        progressSection.style.backgroundColor = '#f8f9fa';
        setTimeout(() => {
            progressSection.style.backgroundColor = '';
        }, 300);

        // Rebuild the entire progress section with updated data
        const progressHtml = `
            <div class="mb-3">
                <strong>Status:</strong>
                <span class="badge bg-${getStatusColor(progress.status)}">
                    ${progress.status.charAt(0).toUpperCase() + progress.status.slice(1)}
                </span>
            </div>

            ${progress.total_revisions > 0 ? `
            <div class="mb-3">
                <strong>Progress:</strong> ${(() => {
                    // Show progress based on processed revisions, not current revision number
                    if (progress.status === 'completed') {
                        return `${progress.total_revisions} / ${progress.total_revisions}`;
                    } else if (progress.current_revision) {
                        // Show processed + 1 (currently processing) out of total
                        let currentlyProcessing = progress.processed_revisions + 1;
                        return `${currentlyProcessing} / ${progress.total_revisions} (processing)`;
                    } else {
                        return `${progress.processed_revisions} / ${progress.total_revisions}`;
                    }
                })()}
                <div class="progress mt-1">
                    <div class="progress-bar ${progress.status === 'completed' ? 'bg-success' : ''}" role="progressbar"
                         style="width: ${(() => {
                             // Calculate progress based on processed revisions
                             if (progress.status === 'completed') {
                                 return '100';
                             }
                             // Use processed revisions + partial progress for current revision
                             let currentProgress = progress.processed_revisions;
                             if (progress.current_revision) {
                                 currentProgress += 0.5; // Add half progress for currently processing revision
                             }
                             let progressPercent = Math.round((currentProgress / progress.total_revisions) * 100);
                             return Math.max(Math.min(progressPercent, 100), 0).toString();
                         })()}%">
                        ${(() => {
                             if (progress.status === 'completed') {
                                 return '100';
                             }
                             // Use processed revisions + partial progress for current revision
                             let currentProgress = progress.processed_revisions;
                             if (progress.current_revision) {
                                 currentProgress += 0.5; // Add half progress for currently processing revision
                             }
                             let progressPercent = Math.round((currentProgress / progress.total_revisions) * 100);
                             return Math.max(Math.min(progressPercent, 100), 0).toString();
                         })()}%
                    </div>
                </div>
            </div>
            ` : ''}

            ${progress.current_revision ? `
            <div class="mb-3">
                <strong>Current Revision:</strong> ${progress.current_revision}
            </div>
            ` : ''}

            ${progress.failed_revisions > 0 ? `
            <div class="mb-3">
                <strong>Failed:</strong> ${progress.failed_revisions}
            </div>
            ` : ''}

            ${progress.started_at ? `
            <div class="mb-3">
                <strong>Started:</strong> ${new Date(progress.started_at).toLocaleString()}
            </div>
            ` : ''}

            ${progress.estimated_completion ? `
            <div class="mb-3">
                <strong>Estimated Completion:</strong> ${new Date(progress.estimated_completion).toLocaleString()}
            </div>
            ` : ''}

            ${progress.error_message ? `
            <div class="alert alert-danger">
                <strong>Error:</strong> ${progress.error_message}
            </div>
            ` : ''}
        `;

        progressSection.innerHTML = progressHtml;

        // Force progress bar update for completed scans
        if (progress.status === 'completed') {
            setTimeout(() => {
                const progressBar = progressSection.querySelector('.progress-bar');
                if (progressBar) {
                    progressBar.style.width = '100%';
                    progressBar.textContent = '100%';
                    progressBar.classList.add('bg-success');
                    console.log('Forced progress bar to 100% for completed scan');
                }
            }, 100);
        }
    }

    function getStatusColor(status) {
        switch (status) {
            case 'completed': return 'success';
            case 'failed': return 'danger';
            case 'cancelled': return 'warning';
            case 'in_progress': return 'primary';
            default: return 'secondary';
        }
    }

    function showScanError(message) {
        const errorAlert = document.getElementById('scan-error-alert');
        const errorMessage = document.getElementById('scan-error-message');

        errorMessage.textContent = message;
        errorAlert.style.display = 'block';

        // Scroll to the error message
        errorAlert.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Also show a brief alert for immediate attention
        alert('Scan Error: ' + message);
    }

    // Start progress polling if scan is active
    {% if scan_progress and scan_progress.status.value == 'in_progress' %}
    progressUpdateInterval = setInterval(updateProgress, 1000); // Update every 1 second
    {% endif %}
});
</script>
{% endblock %}
