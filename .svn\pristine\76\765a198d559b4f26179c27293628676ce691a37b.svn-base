#!/bin/bash
# RepoSense AI Container Entrypoint Script
# Ensures proper file permissions and directory setup before starting the application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}[ENTRYPOINT]${NC} Starting RepoSense AI container setup..."

# Function to log messages
log_info() {
    echo -e "${GREEN}[ENTRYPOINT]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[ENTRYPOINT]${NC} $1"
}

log_error() {
    echo -e "${RED}[ENTRYPOINT]${NC} $1"
}

# Ensure data directories exist with proper permissions
log_info "Setting up data directories..."

# Create directories if they don't exist
mkdir -p /app/data
mkdir -p /app/logs
mkdir -p /app/data/output
mkdir -p /app/data/cache

# Set ownership to appuser (current user should already be appuser)
CURRENT_USER=$(whoami)
if [ "$CURRENT_USER" = "appuser" ]; then
    log_info "Running as appuser - setting up permissions..."
    
    # Ensure all data directories are owned by appuser
    if [ -w /app/data ]; then
        # Only try to change ownership if we have write access to the parent directory
        find /app/data -type d -exec chmod 755 {} \; 2>/dev/null || log_warn "Could not set directory permissions"
        find /app/data -type f -exec chmod 644 {} \; 2>/dev/null || log_warn "Could not set file permissions"
    else
        log_warn "No write access to /app/data - permissions may need to be set externally"
    fi
    
    # Special handling for database file if it exists
    if [ -f "/app/data/documents.db" ]; then
        if [ -w "/app/data/documents.db" ]; then
            chmod 644 /app/data/documents.db
            log_info "Set database file permissions to 644"
        else
            log_warn "Database file exists but is not writable - this may cause issues"
        fi
    fi
    
    # Ensure logs directory is writable
    if [ -d "/app/logs" ] && [ -w "/app/logs" ]; then
        chmod 755 /app/logs 2>/dev/null || log_warn "Could not set logs directory permissions"
        log_info "Set logs directory permissions"
    else
        log_warn "Logs directory is not writable - this is usually okay"
    fi
    
else
    log_warn "Not running as appuser (current: $CURRENT_USER) - skipping permission setup"
fi

# Validate critical directories
log_info "Validating directory setup..."

if [ ! -w "/app/data" ]; then
    log_error "Data directory is not writable! This will cause database issues."
    log_error "Please check Docker volume permissions or run: docker-compose exec --user root reposense-ai chown -R appuser:appuser /app/data"
fi

if [ ! -w "/app/logs" ]; then
    log_warn "Logs directory is not writable - logging may be limited"
fi

# Display environment info
log_info "Environment information:"
echo "  User: $(whoami) ($(id))"
echo "  Working directory: $(pwd)"
echo "  Data directory permissions: $(ls -ld /app/data 2>/dev/null || echo 'Not accessible')"
if [ -f "/app/data/documents.db" ]; then
    echo "  Database file permissions: $(ls -l /app/data/documents.db)"
fi

# Initialize database with proper permissions
log_info "Initializing database..."
if command -v python3 >/dev/null 2>&1; then
    if python3 /usr/local/bin/init_database.py; then
        log_info "Database initialization successful"
    else
        log_warn "Database initialization script failed - continuing anyway"
    fi
else
    log_warn "Python3 not available - skipping database initialization script"
fi

log_info "Container setup complete - starting application..."

# Execute the main command
exec "$@"
