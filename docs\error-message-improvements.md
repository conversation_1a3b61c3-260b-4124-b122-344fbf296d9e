# Error Message Improvements

## Overview

RepoSense AI now provides much more descriptive and helpful error messages when documents cannot be found or accessed, replacing generic "Document not found" messages with specific, actionable information.

## ✅ **Improved Error Messages**

### **1. Document Viewing Errors**

#### **Before:**
- Generic: "Document not found"
- No context about why the document wasn't found

#### **After:**
- **Database not found**: "Document not found. The document with ID 'abc123' may have been deleted or moved."
- **File missing**: "Document file not found. The file '/path/to/document.md' appears to have been moved or deleted from the filesystem."
- **File unreadable**: "Unable to read document content. The file may be corrupted or have permission issues."
- **Orphaned document**: "Document unavailable. This document belongs to a repository that has been removed from the configuration. Repository ID: repo_xyz"

### **2. API Endpoint Errors**

All API endpoints now provide structured error responses with:
- **Descriptive error messages**
- **Error codes** for programmatic handling
- **Specific document IDs** in error messages
- **Enhanced logging** for debugging

#### **Document Deletion API:**
```json
{
  "success": false,
  "message": "Document not found. The document with ID 'abc123' may have been deleted or is no longer available.",
  "error_code": "DOCUMENT_NOT_FOUND"
}
```

#### **Diff Generation API:**
```json
{
  "error": "Document not found. Unable to generate diff for document ID 'abc123'.",
  "error_code": "DOCUMENT_NOT_FOUND"
}
```

#### **AI Suggestions API:**
```json
{
  "error": "Document not found. Unable to generate AI suggestions for document ID 'abc123'.",
  "error_code": "DOCUMENT_NOT_FOUND"
}
```

#### **PDF Generation API:**
```json
{
  "error": "Document not found. Unable to generate PDF for document ID 'abc123'.",
  "error_code": "DOCUMENT_NOT_FOUND"
}
```

### **3. Enhanced Diff Generation Errors**

#### **Before:**
- "Cannot generate diff for this document - missing repository metadata"

#### **After:**
- "Cannot generate diff for this document. The document may be missing repository metadata or the repository may no longer be accessible."
- Includes error code: `DIFF_GENERATION_FAILED`

## **🔍 Error Detection Logic**

### **Document Not Found Scenarios:**

1. **Database Record Missing**: Document ID doesn't exist in database
2. **File System Missing**: Document record exists but file is missing
3. **File Unreadable**: File exists but cannot be read (permissions, corruption)
4. **Orphaned Document**: Document exists but repository was removed from config

### **Orphaned Document Detection:**

The system now automatically detects when a document belongs to a repository that no longer exists in the configuration:

```python
# Check if document is orphaned
config = self.config_manager.load_config()
valid_repo_ids = set(repo.id for repo in config.repositories)
is_orphaned = document.repository_id not in valid_repo_ids
```

## **📊 Benefits**

1. **Better User Experience**: Users understand exactly what went wrong
2. **Easier Debugging**: Specific error messages help identify root causes
3. **Actionable Information**: Users know what steps they can take
4. **Consistent Error Handling**: All endpoints use similar error patterns
5. **Enhanced Logging**: Better log messages for system administrators

## **🛠️ Error Codes**

| Error Code | Description | HTTP Status |
|------------|-------------|-------------|
| `DOCUMENT_NOT_FOUND` | Document doesn't exist in database | 404 |
| `DIFF_GENERATION_FAILED` | Cannot generate diff for document | 404 |

## **📝 User-Facing Messages**

### **Flash Messages (Web Interface):**
- Use appropriate severity levels (error, warning, info)
- Include specific document IDs when helpful
- Provide context about what might have caused the issue

### **API Responses:**
- Structured JSON with error codes
- Descriptive messages suitable for display to users
- Consistent format across all endpoints

## **🔧 Implementation Details**

### **Enhanced Logging:**
All error conditions now include:
- Specific document IDs
- Repository information when relevant
- File paths when applicable
- Warning level logging for debugging

### **File System Checks:**
When document content cannot be read, the system now:
1. Checks if the file exists on disk
2. Provides different messages for missing vs. unreadable files
3. Logs the specific file path for debugging

### **Repository Validation:**
Documents are now validated against current repository configuration to detect orphaned documents and provide appropriate warnings.

## **🚀 Future Enhancements**

Potential improvements:
- Automatic cleanup suggestions for orphaned documents
- File recovery suggestions when files are missing
- Integration with repository status checking
- User-friendly document recovery workflows
