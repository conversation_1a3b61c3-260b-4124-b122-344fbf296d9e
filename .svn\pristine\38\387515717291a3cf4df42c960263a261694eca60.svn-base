# RepoSense AI - Deployment Guide

## 🎯 Unified Deployment Approach

RepoSense AI now uses a **single, simple deployment method** that works for both development and production.

## 🚀 Quick Deployment

### Prerequisites
- Docker and Docker Compose installed
- That's it! No other dependencies needed.

### Deploy RepoSense AI
```bash
# Clone the repository
git clone <your-repo-url>
cd reposense_ai

# Start RepoSense AI (works for both dev and production)
docker-compose up -d

# Access web interface
open http://localhost:5000
```

**That's it!** 🎉 RepoSense AI is now running and ready to configure via the web interface.

## 🔧 Configuration

### Primary Configuration Method
1. **Visit** http://localhost:5000
2. **Click** "Configuration" in the navigation
3. **Configure** your settings:
   - Ollama Host (e.g., `http://your-ollama-server:11434`)
   - Ollama Model (e.g., `qwen3`, `codeqwen:7b-chat-v1.5-q8_0`)
   - Repository settings (SVN/Git URLs, credentials)
   - Email notifications
   - Check intervals
4. **Click** "Save Configuration"

### Optional Environment Overrides
For deployment-specific overrides, create a `.env` file:

```bash
# Copy the example
cp .env.example .env

# Edit .env and uncomment needed overrides:
# OLLAMA_BASE_URL=http://your-ollama-server:11434
# OLLAMA_MODEL=qwen3
```

## 🏗️ Production Deployment

### Basic Production Setup
```bash
# Same command works for production!
docker-compose up -d

# Configure via web interface
open http://your-server:5000
```

### Production Considerations

#### 1. Reverse Proxy (Optional)
For HTTPS and custom domains, use nginx or traefik:

```nginx
# nginx example
server {
    listen 80;
    server_name reposense.yourcompany.com;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 2. Data Persistence
Data is automatically persisted in:
- `./data/` - Configuration and database
- `./logs/` - Application logs

#### 3. Backup Strategy
```bash
# Backup data
tar -czf reposense-backup-$(date +%Y%m%d).tar.gz data/ logs/

# Restore data
tar -xzf reposense-backup-YYYYMMDD.tar.gz
```

## 🛠️ Management Commands

### Using Make (Recommended)
```bash
# Start RepoSense AI
make start

# Stop RepoSense AI
make stop

# View logs
make logs

# Development mode (with debug)
make dev

# Check status
make status

# Show configuration
make config

# Health check
make health
```

### Using Docker Compose Directly
```bash
# Start
docker-compose up -d

# Stop
docker-compose down

# View logs
docker-compose logs -f reposense-ai

# Rebuild
docker-compose build
```

## 🔍 Troubleshooting

### Check Service Status
```bash
make status
# or
docker-compose ps
```

### View Logs
```bash
make logs
# or
docker-compose logs -f reposense-ai
```

### Health Check
```bash
make health
# or
curl http://localhost:5000/health
```

### Configuration Issues
```bash
make config
# or
docker-compose exec reposense-ai python config_summary.py
```

### Reset Configuration
```bash
# Stop service
make stop

# Remove configuration (will be recreated with defaults)
rm data/config.json

# Start service
make start

# Reconfigure via web interface
open http://localhost:5000
```

## 🎯 Migration from Old Deployment

If you're migrating from the old complex deployment setup:

### 1. Migrate Configuration
```bash
# Run migration script to convert environment variables to config file
python migrate_to_config.py
```

### 2. Cleanup Old Files
The old deployment files have been removed:
- ❌ Multiple Dockerfiles
- ❌ Complex build scripts
- ❌ Binary deployment scripts
- ❌ Multiple docker-compose files

### 3. New Simple Approach
- ✅ Single Dockerfile
- ✅ Single docker-compose.yml
- ✅ Web interface configuration
- ✅ Make commands for convenience

## 🎉 Benefits

**Before**: Complex multi-file deployment with environment variable management
**After**: Single command deployment with web interface configuration

- **Setup Time**: 15+ minutes → 30 seconds
- **Files to Manage**: 8+ deployment files → 2 files
- **Configuration**: Environment variables → Web interface
- **Maintenance**: Complex → Simple

The unified approach makes RepoSense AI incredibly easy to deploy and maintain! 🚀
