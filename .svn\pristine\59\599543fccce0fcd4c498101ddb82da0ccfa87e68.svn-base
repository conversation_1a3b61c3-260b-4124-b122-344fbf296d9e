"""
PDF Generation Service for RepoSense AI

This module provides PDF generation capabilities for documents.
Uses reportlab for PDF generation with markdown-like formatting.
"""

import logging
from datetime import datetime
from typing import Optional, Dict, Any
from io import BytesIO

try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib.colors import black, blue, gray, green, red
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Preformatted
    from reportlab.platypus import Table, TableStyle
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False


class PDFGenerator:
    """Service for generating PDF documents"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        if not REPORTLAB_AVAILABLE:
            self.logger.warning("ReportLab not available - PDF generation disabled")
            return
        
        # Initialize styles
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Setup custom paragraph styles"""
        if not REPORTLAB_AVAILABLE:
            return
        
        # Document title style
        self.styles.add(ParagraphStyle(
            name='DocumentTitle',
            parent=self.styles['Title'],
            fontSize=18,
            spaceAfter=12,
            textColor=blue
        ))
        
        # Section header style
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=6,
            spaceBefore=12,
            textColor=black
        ))

        # Sub-header style
        self.styles.add(ParagraphStyle(
            name='SubHeader',
            parent=self.styles['Heading3'],
            fontSize=12,
            spaceAfter=4,
            spaceBefore=8,
            textColor=black,
            fontName='Helvetica-Bold'
        ))

        # Additional heading styles for markdown content
        self.styles.add(ParagraphStyle(
            name='MarkdownH1',
            parent=self.styles['Heading1'],
            fontSize=16,
            spaceAfter=8,
            spaceBefore=12,
            textColor=black,
            fontName='Helvetica-Bold'
        ))

        self.styles.add(ParagraphStyle(
            name='MarkdownH3',
            parent=self.styles['Heading3'],
            fontSize=12,
            spaceAfter=4,
            spaceBefore=8,
            textColor=black,
            fontName='Helvetica-Bold'
        ))
        
        # Code style
        self.styles.add(ParagraphStyle(
            name='CodeBlock',
            parent=self.styles['Code'],
            fontSize=9,
            fontName='Courier',
            leftIndent=20,
            backgroundColor=gray,
            borderColor=black,
            borderWidth=1
        ))
        
        # Metadata style
        self.styles.add(ParagraphStyle(
            name='Metadata',
            parent=self.styles['Normal'],
            fontSize=10,
            textColor=gray
        ))

        # Body style (alias for Normal with slight modifications)
        self.styles.add(ParagraphStyle(
            name='Body',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=6
        ))

        # Diff-specific styles
        self.styles.add(ParagraphStyle(
            name='DiffHeader',
            parent=self.styles['Code'],
            fontSize=9,
            fontName='Courier-Bold',
            textColor=black,
            spaceAfter=2
        ))

        self.styles.add(ParagraphStyle(
            name='DiffHunk',
            parent=self.styles['Code'],
            fontSize=9,
            fontName='Courier-Bold',
            textColor=blue,
            spaceAfter=2
        ))

        self.styles.add(ParagraphStyle(
            name='DiffAdded',
            parent=self.styles['Code'],
            fontSize=9,
            fontName='Courier',
            textColor=green,
            leftIndent=10,
            spaceAfter=1
        ))

        self.styles.add(ParagraphStyle(
            name='DiffRemoved',
            parent=self.styles['Code'],
            fontSize=9,
            fontName='Courier',
            textColor=red,
            leftIndent=10,
            spaceAfter=1
        ))

        self.styles.add(ParagraphStyle(
            name='DiffContext',
            parent=self.styles['Code'],
            fontSize=9,
            fontName='Courier',
            textColor=gray,
            leftIndent=10,
            spaceAfter=1
        ))
    
    def is_available(self) -> bool:
        """Check if PDF generation is available"""
        return REPORTLAB_AVAILABLE
    
    def generate_document_pdf(self, content: str, diff_content: str = "",
                            document_info: Optional[Dict[str, Any]] = None,
                            _document: Optional[Any] = None) -> Optional[bytes]:
        """Generate PDF for a document"""
        
        if not REPORTLAB_AVAILABLE:
            self.logger.error("Cannot generate PDF - ReportLab not available")
            return None
        
        try:
            # Create PDF buffer
            buffer = BytesIO()
            
            # Create document
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # Build content
            story: list = []
            
            # Add title and metadata
            self._add_header(story, document_info or {})

            # Add AI processing information
            self._add_ai_processing_info(story, document_info or {})

            # Add user documentation input
            self._add_user_documentation_input(story, document_info or {})

            # Add user feedback & review
            self._add_user_feedback_review(story, document_info or {})

            # Add main content
            self._add_content(story, content)
            
            # Add diff if available
            if diff_content.strip():
                self._add_diff_section(story, diff_content)
            
            # Add footer
            self._add_footer(story)
            
            # Build PDF
            doc.build(story)
            
            # Get PDF bytes
            pdf_bytes = buffer.getvalue()
            buffer.close()
            
            self.logger.info(f"Generated PDF: {len(pdf_bytes)} bytes")
            return pdf_bytes
            
        except Exception as e:
            self.logger.error(f"PDF generation failed: {e}")
            return None
    
    def _add_header(self, story, document_info: Dict[str, Any]):
        """Add document header with metadata"""

        # Title
        title = document_info.get('title', 'RepoSense AI Document')
        story.append(Paragraph(title, self.styles['DocumentTitle']))
        story.append(Spacer(1, 12))

        # Revision Summary Document section header
        story.append(Paragraph("Revision Summary Document", self.styles['SectionHeader']))
        story.append(Spacer(1, 6))

        # Extended metadata table
        metadata = [
            ['Repository:', document_info.get('repository', 'Unknown')],
            ['Revision:', str(document_info.get('revision', 'Unknown'))],
            ['Author:', document_info.get('author', 'Unknown')],
            ['Date:', document_info.get('date', 'Unknown')],
            ['Filename:', document_info.get('filename', 'Unknown')],
            ['Size:', document_info.get('size', 'Unknown')],
            ['Path:', document_info.get('path', 'Unknown')],
            ['Commit Message:', self._format_commit_message(document_info.get('commit_message', 'No commit message available'))]
        ]

        table = Table(metadata, colWidths=[1.5*inch, 5*inch])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))

        story.append(table)
        story.append(Spacer(1, 12))

        # Files changed section
        story.append(Paragraph("Files Changed:", self.styles['Normal']))
        story.append(Spacer(1, 6))

        changed_files = document_info.get('changed_files', [])
        if changed_files:
            # Create files list
            for file_path in changed_files:
                story.append(Paragraph(f"• <font name='Courier'>{file_path}</font>", self.styles['Normal']))

            story.append(Spacer(1, 6))
            files_count = document_info.get('files_changed_count', 'Files changed information not available')
            story.append(Paragraph(files_count, self.styles['Metadata']))
        else:
            story.append(Paragraph("No file change information available", self.styles['Metadata']))

        story.append(Spacer(1, 20))

    def _add_ai_processing_info(self, story, document_info: dict):
        """Add AI processing information section"""

        story.append(Paragraph("AI Processing Information", self.styles['SectionHeader']))
        story.append(Spacer(1, 12))

        # Create table data for AI information
        ai_data = []

        # AI Model and Host
        ai_model = document_info.get('ai_model', 'Unknown')
        ai_host = document_info.get('ai_host', 'Not configured')
        ai_data.append(['AI Model:', ai_model])
        ai_data.append(['AI Host:', ai_host])

        # Processing time
        processing_time = document_info.get('processing_time')
        if processing_time:
            ai_data.append(['Processing Time:', processing_time])

        # AI Analysis Results
        code_review = document_info.get('code_review_recommended')
        if code_review is not None:
            review_text = 'Recommended'
            if code_review:
                priority = document_info.get('code_review_priority')
                if priority:
                    review_text += f' ({priority} Priority)'
            else:
                review_text = 'Not Required'
            ai_data.append(['Code Review:', review_text])

        risk_level = document_info.get('risk_level')
        if risk_level:
            ai_data.append(['Risk Level:', risk_level])

        doc_impact = document_info.get('documentation_impact')
        if doc_impact is not None:
            impact_text = 'Update Needed' if doc_impact else 'No Update Required'
            ai_data.append(['Documentation Impact:', impact_text])

        if ai_data:
            # Create table
            ai_table = Table(ai_data, colWidths=[2*inch, 4*inch])
            ai_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 0.5, gray),
                ('BACKGROUND', (0, 0), (0, -1), gray),
                ('TEXTCOLOR', (0, 0), (0, -1), black),
                ('ROWBACKGROUNDS', (0, 0), (-1, -1), [None, gray]),
                ('LEFTPADDING', (0, 0), (-1, -1), 6),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 4),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
            ]))

            story.append(ai_table)
            story.append(Spacer(1, 20))

    def _add_user_documentation_input(self, story, document_info: dict):
        """Add user documentation input section if available"""

        user_input = document_info.get('user_documentation_input')
        user_suggestions = document_info.get('user_documentation_suggestions')
        input_by = document_info.get('user_documentation_input_by')
        input_date = document_info.get('user_documentation_input_date')

        if user_input or user_suggestions:
            story.append(Paragraph("Product Documentation Feedback", self.styles['SectionHeader']))
            story.append(Spacer(1, 12))

            if user_input:
                story.append(Paragraph("Product Documentation Content:", self.styles['SubHeader']))
                story.append(Spacer(1, 6))

                # Format user input as preformatted text to preserve formatting
                user_input_formatted = user_input.replace('\n', '<br/>')
                story.append(Paragraph(user_input_formatted, self.styles['Body']))
                story.append(Spacer(1, 12))

            if user_suggestions:
                story.append(Paragraph("Documentation Feedback & Suggestions:", self.styles['SubHeader']))
                story.append(Spacer(1, 6))

                # Format suggestions as preformatted text to preserve formatting
                suggestions_formatted = user_suggestions.replace('\n', '<br/>')
                story.append(Paragraph(suggestions_formatted, self.styles['Body']))
                story.append(Spacer(1, 12))

            # Add attribution if available
            if input_by or input_date:
                attribution_parts = []
                if input_by:
                    attribution_parts.append(f"Documented by: {input_by}")
                if input_date:
                    attribution_parts.append(f"Date: {input_date}")

                attribution_text = " | ".join(attribution_parts)
                story.append(Paragraph(f"<i>{attribution_text}</i>", self.styles['Metadata']))
                story.append(Spacer(1, 20))

    def _add_user_feedback_review(self, story, document_info: dict):
        """Add user feedback & review section if available"""

        # Check if any user feedback data is available
        has_ai_recommendation = any([
            document_info.get('code_review_recommended'),
            document_info.get('code_review_priority')
        ])

        has_code_review = any([
            document_info.get('user_code_review_status'),
            document_info.get('user_code_review_comments'),
            document_info.get('user_code_review_reviewer')
        ])

        has_doc_feedback = any([
            document_info.get('user_documentation_rating'),
            document_info.get('user_documentation_comments'),
            document_info.get('user_documentation_updated_by')
        ])

        has_risk_assessment = any([
            document_info.get('user_risk_assessment_override'),
            document_info.get('user_risk_assessment_comments'),
            document_info.get('user_risk_assessment_updated_by')
        ])

        if has_ai_recommendation or has_code_review or has_doc_feedback or has_risk_assessment:
            story.append(Paragraph("User Feedback & Review", self.styles['SectionHeader']))
            story.append(Spacer(1, 12))

            # Create table data for user feedback
            feedback_data = []

            # AI Recommendation Section
            if has_ai_recommendation:
                feedback_data.append(['AI Code Review Recommendation:', ''])

                if document_info.get('code_review_recommended'):
                    feedback_data.append(['  Recommendation:', '⚠️ Review Recommended'])

                    priority = document_info.get('code_review_priority')
                    if priority:
                        feedback_data.append(['  Priority:', priority])

                feedback_data.append(['', ''])  # Spacer

            # Code Review Section
            if has_code_review:
                feedback_data.append(['Code Review Status:', ''])

                status = document_info.get('user_code_review_status')
                if status:
                    status_display = {
                        'approved': '✅ Approved',
                        'needs_changes': '🔄 Needs Changes',
                        'rejected': '❌ Rejected',
                        'in_progress': '⏳ In Progress'
                    }.get(status, status)
                    feedback_data.append(['  Status:', status_display])

                reviewer = document_info.get('user_code_review_reviewer')
                if reviewer:
                    feedback_data.append(['  Reviewer:', reviewer])

                comments = document_info.get('user_code_review_comments')
                if comments:
                    feedback_data.append(['  Comments:', comments])

                review_date = document_info.get('user_code_review_date')
                if review_date:
                    feedback_data.append(['  Review Date:', review_date])

                feedback_data.append(['', ''])  # Spacer

            # Documentation Quality Section
            if has_doc_feedback:
                feedback_data.append(['Documentation Quality:', ''])

                rating = document_info.get('user_documentation_rating')
                if rating:
                    stars = '⭐' * int(rating)
                    feedback_data.append(['  Rating:', f'{stars} ({rating}/5)'])

                updated_by = document_info.get('user_documentation_updated_by')
                if updated_by:
                    feedback_data.append(['  Updated By:', updated_by])

                doc_comments = document_info.get('user_documentation_comments')
                if doc_comments:
                    feedback_data.append(['  Comments:', doc_comments])

                doc_date = document_info.get('user_documentation_updated_date')
                if doc_date:
                    feedback_data.append(['  Update Date:', doc_date])

                feedback_data.append(['', ''])  # Spacer

            # Risk Assessment Section
            if has_risk_assessment:
                feedback_data.append(['Risk Assessment:', ''])

                risk_override = document_info.get('user_risk_assessment_override')
                if risk_override:
                    risk_display = {
                        'LOW': '🟢 LOW Risk',
                        'MEDIUM': '🟡 MEDIUM Risk',
                        'HIGH': '🔴 HIGH Risk'
                    }.get(risk_override, risk_override)
                    feedback_data.append(['  User Assessment:', risk_display])

                risk_assessor = document_info.get('user_risk_assessment_updated_by')
                if risk_assessor:
                    feedback_data.append(['  Assessed By:', risk_assessor])

                risk_comments = document_info.get('user_risk_assessment_comments')
                if risk_comments:
                    feedback_data.append(['  Comments:', risk_comments])

                risk_date = document_info.get('user_risk_assessment_updated_date')
                if risk_date:
                    feedback_data.append(['  Assessment Date:', risk_date])

            if feedback_data:
                # Create table
                feedback_table = Table(feedback_data, colWidths=[2*inch, 4*inch])
                feedback_table.setStyle(TableStyle([
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                    ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                    ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 0), (-1, -1), 10),
                    ('GRID', (0, 0), (-1, -1), 0.5, gray),
                    ('BACKGROUND', (0, 0), (0, -1), gray),
                    ('TEXTCOLOR', (0, 0), (0, -1), black),
                    ('ROWBACKGROUNDS', (0, 0), (-1, -1), [None, gray]),
                    ('LEFTPADDING', (0, 0), (-1, -1), 6),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                    ('TOPPADDING', (0, 0), (-1, -1), 4),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
                ]))

                story.append(feedback_table)
                story.append(Spacer(1, 20))

    def _add_content(self, story, content: str):
        """Add main document content with proper markdown-style formatting"""

        story.append(Paragraph("Document Content", self.styles['SectionHeader']))
        story.append(Spacer(1, 12))

        # Process content line by line to handle markdown-style formatting
        lines = content.split('\n')
        current_paragraph: list[str] = []
        in_code_block = False

        for line in lines:
            line = line.rstrip()

            # Handle code blocks
            if line.startswith('```'):
                if current_paragraph:
                    self._add_paragraph(story, '\n'.join(current_paragraph))
                    current_paragraph = []
                in_code_block = not in_code_block
                continue

            if in_code_block:
                story.append(Preformatted(line, self.styles['Code']))
                continue

            # Handle headers
            if line.startswith('## '):
                if current_paragraph:
                    self._add_paragraph(story, '\n'.join(current_paragraph))
                    current_paragraph = []
                header_text = line[3:].strip()
                story.append(Spacer(1, 12))
                story.append(Paragraph(header_text, self.styles['Heading2']))
                story.append(Spacer(1, 6))
                continue
            elif line.startswith('# '):
                if current_paragraph:
                    self._add_paragraph(story, '\n'.join(current_paragraph))
                    current_paragraph = []
                header_text = line[2:].strip()
                story.append(Spacer(1, 12))
                story.append(Paragraph(header_text, self.styles['MarkdownH1']))
                story.append(Spacer(1, 8))
                continue
            elif line.startswith('### '):
                if current_paragraph:
                    self._add_paragraph(story, '\n'.join(current_paragraph))
                    current_paragraph = []
                header_text = line[4:].strip()
                story.append(Spacer(1, 10))
                story.append(Paragraph(header_text, self.styles['MarkdownH3']))
                story.append(Spacer(1, 4))
                continue

            # Handle bullet points
            if line.startswith('- ') or line.startswith('* '):
                if current_paragraph:
                    self._add_paragraph(story, '\n'.join(current_paragraph))
                    current_paragraph = []
                bullet_text = line[2:].strip()
                story.append(Paragraph(f"• {bullet_text}", self.styles['Normal']))
                story.append(Spacer(1, 3))
                continue

            # Handle empty lines (paragraph breaks)
            if not line.strip():
                if current_paragraph:
                    self._add_paragraph(story, '\n'.join(current_paragraph))
                    current_paragraph = []
                continue

            # Regular content line
            current_paragraph.append(line)

        # Add any remaining paragraph
        if current_paragraph:
            self._add_paragraph(story, '\n'.join(current_paragraph))

    def _add_paragraph(self, story, text: str):
        """Add a formatted paragraph to the story"""
        if text.strip():
            # Handle inline code `code` first (more complex replacement)
            import re
            text = re.sub(r'`([^`]+)`', r'<font name="Courier">\1</font>', text)

            # Handle bold text **text**
            text = re.sub(r'\*\*([^*]+)\*\*', r'<b>\1</b>', text)

            # Handle italic text *text* (but not ** which we already handled)
            text = re.sub(r'(?<!\*)\*([^*]+)\*(?!\*)', r'<i>\1</i>', text)

            story.append(Paragraph(text.strip(), self.styles['Normal']))
            story.append(Spacer(1, 8))
    
    def _add_diff_section(self, story, diff_content: str):
        """Add diff section to PDF with enhanced formatting"""

        story.append(Spacer(1, 20))
        story.append(Paragraph("Code Changes (Diff)", self.styles['SectionHeader']))
        story.append(Spacer(1, 12))

        # Process diff content line by line for better formatting
        lines = diff_content.split('\n')

        for line in lines:
            if not line.strip():
                story.append(Spacer(1, 3))
                continue

            # File headers (Index:, ===, ---, +++)
            if (line.startswith('Index:') or
                line.startswith('===') or
                line.startswith('---') or
                line.startswith('+++')):
                story.append(Paragraph(f'<font name="Helvetica-Bold">{line}</font>', self.styles['DiffHeader']))
                story.append(Spacer(1, 3))

            # Hunk headers (@@ ... @@)
            elif line.startswith('@@'):
                story.append(Paragraph(f'<font name="Helvetica-Bold" color="blue">{line}</font>', self.styles['DiffHunk']))
                story.append(Spacer(1, 3))

            # Added lines (+)
            elif line.startswith('+') and not line.startswith('+++'):
                # Remove the + and format as added line
                content = line[1:] if len(line) > 1 else ''
                story.append(Paragraph(f'<font name="Courier" color="green">+ {content}</font>', self.styles['DiffAdded']))

            # Removed lines (-)
            elif line.startswith('-') and not line.startswith('---'):
                # Remove the - and format as removed line
                content = line[1:] if len(line) > 1 else ''
                story.append(Paragraph(f'<font name="Courier" color="red">- {content}</font>', self.styles['DiffRemoved']))

            # Context lines (unchanged)
            else:
                # Regular context line
                story.append(Paragraph(f'<font name="Courier" color="gray">  {line}</font>', self.styles['DiffContext']))

        story.append(Spacer(1, 12))

    def _format_commit_message(self, commit_message):
        """Format commit message for PDF display with proper text wrapping"""
        if not commit_message or commit_message.strip() == '':
            return 'No commit message available'

        # Handle multi-line commit messages by preserving line breaks
        # and properly wrapping long lines instead of truncating
        lines = commit_message.split('\n')
        formatted_lines = []

        for line in lines:
            line = line.strip()
            if line:
                # Wrap long lines instead of truncating them
                if len(line) > 80:
                    # Use textwrap to properly wrap long lines
                    import textwrap
                    wrapped_lines = textwrap.wrap(line, width=80, break_long_words=False, break_on_hyphens=True)
                    formatted_lines.extend(wrapped_lines)
                else:
                    formatted_lines.append(line)

        # Return the complete commit message - no truncation
        return '\n'.join(formatted_lines) if formatted_lines else 'No commit message available'

    def _add_footer(self, story):
        """Add document footer"""
        
        story.append(Spacer(1, 20))
        
        # Generation info
        footer_text = f"Generated by RepoSense AI on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        story.append(Paragraph(footer_text, self.styles['Metadata']))


# Fallback class when ReportLab is not available
class PDFGeneratorFallback:
    """Fallback PDF generator when ReportLab is not available"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def is_available(self) -> bool:
        return False
    
    def generate_document_pdf(self, *_args, **_kwargs) -> None:
        self.logger.error("PDF generation not available - ReportLab not installed")
        return None


# Use fallback if ReportLab is not available
if not REPORTLAB_AVAILABLE:
    PDFGenerator = PDFGeneratorFallback  # type: ignore
