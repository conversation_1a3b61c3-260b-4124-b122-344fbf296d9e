## Summary
This commit addresses an issue with string formatting in the `web_interface.py` file, specifically within the document handling section. The previous version of the code had extra spaces around the error messages when flashing notifications to the user interface. This commit removes these unnecessary spaces for better readability and adherence to PEP8 style guidelines.

## Technical Details
The change is focused on improving the formatting of string literals used with the `flash()` function in Flask applications, which sends messages from the server back to the client (usually displayed in a template). Previously, there were extraneous spaces around the strings causing inconsistent formatting and potential confusion for developers maintaining the code. This commit ensures that the error messages follow consistent, clean formatting by removing these unnecessary spaces.

## Impact Assessment
- **Codebase**: Minimal impact on the codebase. It only modifies string literals without affecting functionality or introducing new logic.
- **Users**: None, as this change is purely cosmetic and does not alter user experience or system behavior.
- **System Functionality**: No changes to system functionality as the error handling logic remains untouched.

## Code Review Recommendation
This commit should still be code reviewed because:
- Although the changes are minor, they involve touching core application strings, which might prompt discussion on string formatting conventions and best practices.
- Risk level is low due to the nature of the change.
- Areas affected are limited to the presentation layer (UI).
- Potential for introducing bugs is virtually nonexistent.
- No security implications, as this change does not affect data handling or system access control.

## Documentation Impact
This commit has no impact on documentation:
- User-facing features remain unchanged.
- APIs and interfaces are unaffected.
- Configuration options are not modified.
- Deployment procedures remain the same.
- No additional documentation updates are necessary due to this change being purely stylistic and functional in nature.

## Recommendations
1. Ensure that the development team is aware of the formatting standard (no extra spaces around string literals in brackets) for consistent code style.
2. Consider adding a note in the project's coding guidelines if not already present, specifying proper string formatting within templates to avoid such issues in the future.