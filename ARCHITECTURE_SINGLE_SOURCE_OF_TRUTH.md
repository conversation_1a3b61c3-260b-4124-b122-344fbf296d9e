# Single Source of Truth Architecture

## Fundamental Principle

**The repository is the ONLY source of truth for commit data. Generated files (markdown, PDFs, etc.) are OUTPUT ONLY and should never be parsed for data extraction.**

## Correct Data Flow

```
Repository (Single Source of Truth)
    ↓
Repository Backend (SVN/Git)
    ↓
CommitInfo Object
    ↓
Database Storage
    ↓
UI Display & File Generation
```

## Anti-Patterns to Avoid

### ❌ WRONG: Parsing Generated Files
```python
# DON'T DO THIS
content = read_markdown_file()
author = extract_field(content, "Author")
commit_message = extract_field(content, "Message")
```

### ✅ CORRECT: Query Repository Backend
```python
# DO THIS
backend = get_backend_for_repository(repo_config)
commit_info = backend.get_commit_info(repo, revision)
author = commit_info.author
commit_message = commit_info.message
```

## Implementation Guidelines

### 1. Repository Backends (Primary Data Source)
- **Purpose**: Interface with actual repositories (SVN, Git)
- **Responsibility**: Extract commit data directly from repository
- **Methods**: `get_commit_info()`, `get_diff()`, `get_latest_revision()`
- **Rule**: Always query the repository, never cache stale data

### 2. Document Processor (Data Consumer)
- **Purpose**: Process documents and store metadata
- **Rule**: MUST use repository backend as primary source
- **Fallback**: Only use markdown content if repository is completely unavailable
- **Implementation**: 
  ```python
  # Primary: Repository backend
  commit_info = self._get_commit_info_from_repository(repo_id, revision)
  if commit_info:
      # Use repository data
  else:
      # Fallback: markdown content (with warning)
  ```

### 3. Database Storage (Normalized Data)
- **Purpose**: Store processed commit data for fast access
- **Rule**: Data comes from repository backend, not generated files
- **Fields**: All commit fields should be populated from CommitInfo object

### 4. UI Display (Data Presentation)
- **Purpose**: Display commit information to users
- **Rule**: Get data from database, which got it from repository
- **Fallback**: Show clear messages when data is unavailable

### 5. File Generation (Output Only)
- **Purpose**: Generate markdown, PDF, email files
- **Rule**: These are OUTPUT ONLY - never parse them for data
- **Source**: Get data from database or repository backend

## Data Consistency Rules

### Commit Information
- **Author**: Always from `commit_info.author`
- **Message**: Always from `commit_info.message`
- **Date**: Always from `commit_info.date`
- **Changed Files**: Always from `commit_info.changed_paths`
- **Diff**: Always from `backend.get_diff()`

### Repository Information
- **URL**: From repository configuration
- **Type**: From repository backend type
- **Credentials**: From repository configuration

### Generated Content
- **Documentation**: From AI analysis of CommitInfo
- **Risk Assessment**: From AI analysis of CommitInfo
- **Code Review**: From AI analysis of CommitInfo

## Error Handling

### Repository Unavailable
```python
commit_info = backend.get_commit_info(repo, revision)
if not commit_info:
    logger.warning(f"Repository unavailable for {repo.name} revision {revision}")
    # Use fallback data with clear indication
    author = "Unknown (Repository Unavailable)"
    commit_message = "Commit message unavailable (Repository Unavailable)"
```

### Partial Data
```python
# Handle missing fields gracefully
author = commit_info.author or "Unknown"
commit_message = commit_info.message or "No commit message available"
changed_paths = commit_info.changed_paths or []
```

## Migration Strategy

### Phase 1: Fix Document Processor ✅
- Modified `document_processor.py` to use repository backend first
- Markdown content only as absolute fallback
- Added clear warnings when using fallback

### Phase 2: Audit All Data Extraction
- [ ] Review all `_extract_field()` usage
- [ ] Ensure no other components parse generated files
- [ ] Verify all database writes use repository data

### Phase 3: Remove Secondary Sources
- [ ] Remove unnecessary markdown parsing code
- [ ] Simplify data extraction logic
- [ ] Add validation to prevent future violations

## Testing Guidelines

### Unit Tests
- Mock repository backends, not file content
- Test with real CommitInfo objects
- Verify fallback behavior when repository unavailable

### Integration Tests
- Test with real repository connections
- Verify data consistency across components
- Test error handling for unavailable repositories

## Code Review Checklist

- [ ] Does this code query the repository backend?
- [ ] Is markdown/file parsing only used as absolute fallback?
- [ ] Are error messages clear about data source?
- [ ] Is the data flow Repository → Backend → Database → UI?
- [ ] Are generated files treated as output-only?

## Benefits of This Architecture

1. **Data Consistency**: Same commit always has same data
2. **Reliability**: Direct from source, no parsing errors
3. **Maintainability**: Single extraction logic to maintain
4. **Scalability**: Repository backends can be optimized independently
5. **Accuracy**: No data loss from format changes
6. **Debugging**: Clear data lineage for troubleshooting

## Conclusion

By following the Single Source of Truth principle, we ensure that:
- Repository data is always accurate and consistent
- Generated files are truly output-only
- The system is maintainable and reliable
- Data flows in one clear direction
- Debugging and troubleshooting is straightforward

**Remember: The repository is the source of truth. Everything else is derived data.**
