#!/usr/bin/env python3
"""
Test Enhanced Prompt Integration
Tests that enhanced prompts are actually being used in the document processing pipeline
"""

import sys
import logging
from pathlib import Path
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from metadata_extractor import MetadataExtractor
from document_database import DocumentRecord
from models import Config


def setup_logging():
    """Setup logging for tests"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def create_test_config() -> Config:
    """Create a test configuration"""
    config = Config()
    config.ollama_host = "http://localhost:11434"
    config.ollama_model = "qwen3"
    config.use_enhanced_prompts = True
    config.enhanced_prompts_fallback = True
    return config


def create_test_document() -> DocumentRecord:
    """Create a test document record"""
    return DocumentRecord(
        id="test-doc-integration",
        repository_id="test-repo",
        repository_name="reposense_ai",
        revision=456,
        date=datetime.now(),
        filename="revision_456.md",
        filepath="/app/data/output/test/revision_456.md",
        size=2048,
        author="integration_test",
        commit_message="Implement user authentication with OAuth2 and JWT tokens",
        changed_paths=["src/auth/oauth_handler.py", "src/auth/jwt_service.py", "config/auth_config.json"],
        repository_url="https://github.com/test/reposense_ai",
        repository_type="git"
    )


class MockConfigManager:
    """Mock config manager for testing"""
    
    def __init__(self, config: Config):
        self.config = config
    
    def load_config(self) -> Config:
        return self.config


class MockOllamaClient:
    """Mock Ollama client that logs what prompts it receives"""
    
    def __init__(self):
        self.last_system_prompt = None
        self.last_user_prompt = None
        self.call_count = 0
    
    def call_ollama(self, user_prompt: str, system_prompt: str, model: str = None) -> str:
        """Mock call that captures prompts for inspection"""
        self.call_count += 1
        self.last_system_prompt = system_prompt
        self.last_user_prompt = user_prompt
        
        print(f"\n🔍 Mock Ollama Call #{self.call_count}")
        print(f"📝 System prompt length: {len(system_prompt)} characters")
        print(f"📝 User prompt length: {len(user_prompt)} characters")
        
        # Check if enhanced prompts are being used
        enhanced_indicators = [
            "CONTEXT-SPECIFIC GUIDANCE",
            "SECURITY CHANGE",
            "PRODUCTION",
            "CHANGE ANALYSIS",
            "Impact Areas"
        ]
        
        enhanced_count = sum(1 for indicator in enhanced_indicators 
                           if indicator in system_prompt or indicator in user_prompt)
        
        if enhanced_count > 0:
            print(f"✅ Enhanced prompts detected! ({enhanced_count} indicators found)")
            print(f"🎯 Indicators found: {[ind for ind in enhanced_indicators if ind in system_prompt or ind in user_prompt]}")
        else:
            print("❌ Basic prompts detected (no enhanced indicators)")
        
        # Return a mock JSON response
        return '''
        {
            "code_review_recommended": true,
            "code_review_priority": "HIGH",
            "risk_level": "HIGH",
            "documentation_impact": true
        }
        '''


def test_metadata_extractor_integration():
    """Test that MetadataExtractor uses enhanced prompts when available"""
    print("\n=== Testing MetadataExtractor Integration ===")
    
    config = create_test_config()
    config_manager = MockConfigManager(config)
    ollama_client = MockOllamaClient()
    
    # Create metadata extractor with config manager
    extractor = MetadataExtractor(ollama_client, config_manager)
    
    # Create test document
    document = create_test_document()
    
    # Test content that should trigger LLM fallback
    test_content = """
    ## Summary
    This change implements OAuth2 authentication with JWT token support.
    
    ## Files Changed
    - src/auth/oauth_handler.py (new OAuth2 flow implementation)
    - src/auth/jwt_service.py (JWT token generation and validation)
    - config/auth_config.json (authentication configuration)
    
    ## Impact Assessment
    This is a significant security enhancement that affects user authentication.
    """
    
    print(f"📄 Processing document: {document.repository_name} revision {document.revision}")
    print(f"🔧 Enhanced prompts enabled: {config.use_enhanced_prompts}")
    
    # Extract metadata - this should use enhanced prompts
    metadata = extractor.extract_all_metadata(test_content, document)
    
    print(f"\n📊 Extracted metadata:")
    for key, value in metadata.items():
        print(f"  {key}: {value}")
    
    # Verify enhanced prompts were used
    if ollama_client.call_count > 0:
        print(f"\n✅ LLM was called {ollama_client.call_count} time(s)")
        
        # Check if enhanced prompts were used
        system_prompt = ollama_client.last_system_prompt or ""
        user_prompt = ollama_client.last_user_prompt or ""
        
        enhanced_indicators = [
            "CONTEXT-SPECIFIC GUIDANCE",
            "SECURITY CHANGE", 
            "PRODUCTION",
            "CHANGE ANALYSIS"
        ]
        
        found_indicators = [ind for ind in enhanced_indicators 
                          if ind in system_prompt or ind in user_prompt]
        
        if found_indicators:
            print(f"🎉 SUCCESS: Enhanced prompts are being used!")
            print(f"🔍 Found indicators: {found_indicators}")
            return True
        else:
            print(f"❌ FAILURE: Enhanced prompts not detected")
            print(f"📝 System prompt preview: {system_prompt[:200]}...")
            return False
    else:
        print("❌ FAILURE: LLM was not called")
        return False


def main():
    """Run integration tests"""
    print("🧪 Enhanced Prompt Integration Tests")
    print("=" * 50)
    
    setup_logging()
    
    try:
        success = test_metadata_extractor_integration()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 Integration test PASSED!")
            print("✅ Enhanced prompts are properly integrated into the document processing pipeline")
        else:
            print("❌ Integration test FAILED!")
            print("⚠️  Enhanced prompts are not being used in the document processing pipeline")
        
        return success
        
    except Exception as e:
        print(f"\n❌ Integration test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
