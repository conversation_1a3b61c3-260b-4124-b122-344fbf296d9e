#!/usr/bin/env python3
"""
Test Product Documentation RAG Integration
Tests that product documentation files are being read and integrated into enhanced prompts
"""

import sys
import logging
from pathlib import Path
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from prompt_templates import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PromptTemplateManager
from document_database import DocumentRecord
from models import Config


def setup_logging():
    """Setup logging for tests"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def create_test_repository_with_docs():
    """Create a test repository with product documentation files"""
    test_repo_path = Path("/tmp/test_product_docs_repo")
    test_repo_path.mkdir(exist_ok=True)
    
    # Create README.md
    readme_content = """# RepoSense AI - Repository Intelligence Platform

RepoSense AI is an advanced repository monitoring and analysis platform that provides intelligent insights into code changes, automated documentation generation, and risk assessment for development teams.

## Key Features

- **Automated Code Review Recommendations**: AI-powered analysis of code changes
- **Risk Assessment**: Intelligent evaluation of change impact and deployment risks  
- **Documentation Generation**: Automatic creation of revision summaries and changelogs
- **Multi-Repository Support**: Monitor SVN and Git repositories simultaneously
- **Web Dashboard**: Comprehensive web interface for repository management
- **Email Notifications**: Automated alerts for critical changes

## Technical Stack

- **Backend**: Python 3.11+ with Flask web framework
- **AI/ML**: Ollama integration for local LLM processing
- **Database**: SQLite for document storage and metadata
- **Frontend**: Bootstrap-based responsive web interface
- **Containerization**: Docker and Docker Compose support

## Target Audience

This platform is designed for development teams, DevOps engineers, and technical leads who need intelligent repository monitoring and automated code analysis capabilities.

## Getting Started

1. Configure your repositories in the web interface
2. Set up Ollama for AI analysis
3. Enable monitoring for automatic change detection
4. Review generated documentation and recommendations
"""
    
    (test_repo_path / "README.md").write_text(readme_content)
    
    # Create CHANGELOG.md
    changelog_content = """# Changelog

All notable changes to RepoSense AI will be documented in this file.

## [2.1.0] - 2025-08-10

### Added
- Enhanced prompt engineering with contextual analysis
- Product documentation RAG integration
- Multi-encoding support for document processing
- Side-by-side diff viewer with HTML tables

### Changed
- Improved AI analysis accuracy with repository context
- Enhanced error handling for binary files
- Updated web interface with better user feedback

### Fixed
- UTF-8 encoding issues in document processing
- Progress calculation for non-starting revision ranges
- Database schema compatibility issues

## [2.0.0] - 2025-08-01

### Added
- User feedback system for AI recommendations
- Hybrid AI analysis with heuristic fallbacks
- On-demand diff generation
- Comprehensive test suite

### Breaking Changes
- Database schema updates require migration
- Configuration file format changes
"""
    
    (test_repo_path / "CHANGELOG.md").write_text(changelog_content)
    
    # Create package.json (for Node.js project detection)
    package_json_content = """{
  "name": "reposense-ai",
  "version": "2.1.0",
  "description": "Advanced repository monitoring and analysis platform with AI-powered insights",
  "keywords": ["repository", "monitoring", "ai", "code-review", "documentation", "svn", "git"],
  "scripts": {
    "start": "python start_reposense_ai.py",
    "test": "python -m pytest tests/",
    "build": "docker build -t reposense-ai .",
    "deploy": "docker-compose up -d"
  },
  "repository": {
    "type": "git",
    "url": "https://github.com/example/reposense-ai"
  }
}"""
    
    (test_repo_path / "package.json").write_text(package_json_content)
    
    print(f"📁 Created test repository with product docs at: {test_repo_path}")
    return test_repo_path


def create_test_document_record(repo_path: Path) -> DocumentRecord:
    """Create a test document record pointing to the test repository"""
    return DocumentRecord(
        id="test-product-doc-rag",
        repository_id="test-repo-with-docs",
        repository_name="reposense_ai",
        revision=25,
        date=datetime.now(),
        filename="revision_25.md",
        filepath=str(repo_path / "docs" / "revision_25.md"),
        size=3000,
        author="test_user",
        commit_message="Add new user authentication features with OAuth2 support",
        changed_paths=[
            "src/auth/oauth_handler.py",
            "src/auth/user_manager.py", 
            "templates/login.html",
            "docs/authentication.md"
        ],
        repository_type="git"
    )


def test_product_documentation_extraction():
    """Test that product documentation is being extracted correctly"""
    print("\n=== Testing Product Documentation Extraction ===")
    
    # Create test repository with docs
    repo_path = create_test_repository_with_docs()
    
    # Create test document record
    document_record = create_test_document_record(repo_path)
    
    # Initialize context analyzer
    config = Config()
    config.use_enhanced_prompts = True
    context_analyzer = ContextAnalyzer(config)
    
    # Analyze change context (this should extract product docs)
    print("🔍 Analyzing change context with product documentation...")
    context = context_analyzer.analyze_change_context(
        document=document_record,
        changed_files=document_record.changed_paths,
        commit_message=document_record.commit_message
    )
    
    # Check if product documentation was extracted
    print(f"\n📊 Product Documentation Context Results:")
    print(f"  Product Overview: {context.product_overview[:100]}..." if context.product_overview else "  Product Overview: Not extracted")
    print(f"  Key Features: {context.key_features[:100]}..." if context.key_features else "  Key Features: Not extracted")
    print(f"  Technical Stack: {context.technical_stack}" if context.technical_stack else "  Technical Stack: Not extracted")
    print(f"  User Audience: {context.user_audience}" if context.user_audience else "  User Audience: Not extracted")
    print(f"  Changelog Structure: {context.changelog_structure}" if context.changelog_structure else "  Changelog Structure: Not extracted")
    print(f"  Documentation Style: {context.documentation_style}" if context.documentation_style else "  Documentation Style: Not extracted")
    
    # Count how many fields were extracted
    extracted_fields = sum(1 for field in [
        context.product_overview,
        context.key_features, 
        context.technical_stack,
        context.user_audience,
        context.changelog_structure,
        context.documentation_style
    ] if field)
    
    print(f"\n📈 Extraction Summary: {extracted_fields}/6 fields extracted")
    
    return context, extracted_fields >= 3  # Success if at least 3 fields extracted


def test_enhanced_prompts_with_product_context():
    """Test that enhanced prompts include product documentation context"""
    print("\n=== Testing Enhanced Prompts with Product Context ===")
    
    # Get context from previous test
    context, extraction_success = test_product_documentation_extraction()
    
    if not extraction_success:
        print("❌ Product documentation extraction failed, skipping prompt test")
        return False
    
    # Initialize prompt template manager
    config = Config()
    config.use_enhanced_prompts = True
    prompt_manager = PromptTemplateManager(config)
    
    # Generate enhanced prompts
    test_content = """
    ## Summary
    This revision adds OAuth2 authentication support to the RepoSense AI platform.
    
    ## Changes Made
    - Implemented OAuth2 flow in oauth_handler.py
    - Updated user management system
    - Added login template with OAuth2 options
    - Updated authentication documentation
    
    ## Impact Assessment
    This change affects user authentication and requires careful testing.
    """
    
    print("🎨 Generating enhanced prompts with product context...")
    system_prompt, user_prompt = prompt_manager.get_metadata_extraction_prompt(test_content, context)
    
    # Check if product context is included in prompts
    product_context_indicators = [
        "PRODUCT CONTEXT:",
        "KEY FEATURES:",
        "TARGET AUDIENCE:",
        "TECHNICAL STACK:",
        "repository monitoring",
        "AI-powered analysis"
    ]
    
    found_indicators = []
    for indicator in product_context_indicators:
        if indicator in system_prompt or indicator in user_prompt:
            found_indicators.append(indicator)
    
    print(f"\n📝 Enhanced Prompt Analysis:")
    print(f"  System prompt length: {len(system_prompt)} characters")
    print(f"  User prompt length: {len(user_prompt)} characters")
    print(f"  Product context indicators found: {len(found_indicators)}")
    print(f"  Indicators: {found_indicators}")
    
    # Show sample of enhanced prompts
    print(f"\n📋 System Prompt Sample (first 300 chars):")
    print(f"```\n{system_prompt[:300]}...\n```")
    
    print(f"\n📋 User Prompt Sample (first 400 chars):")
    print(f"```\n{user_prompt[:400]}...\n```")
    
    return len(found_indicators) >= 2  # Success if at least 2 product context indicators found


def main():
    """Run product documentation RAG tests"""
    print("🧪 Product Documentation RAG Integration Tests")
    print("=" * 60)
    
    setup_logging()
    
    try:
        # Test product documentation extraction
        context, extraction_success = test_product_documentation_extraction()
        
        # Test enhanced prompts with product context
        prompt_success = test_enhanced_prompts_with_product_context()
        
        print("\n" + "=" * 60)
        if extraction_success and prompt_success:
            print("🎉 Product Documentation RAG tests PASSED!")
            print("✅ Product documentation is being extracted and integrated into enhanced prompts")
            print("📈 AI recommendations will now be more product-aware and contextual")
        else:
            print("⚠️  Product Documentation RAG tests completed with issues")
            if not extraction_success:
                print("❌ Product documentation extraction failed")
            if not prompt_success:
                print("❌ Product context integration into prompts failed")
        
        return extraction_success and prompt_success
        
    except Exception as e:
        print(f"\n❌ Product Documentation RAG tests failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
