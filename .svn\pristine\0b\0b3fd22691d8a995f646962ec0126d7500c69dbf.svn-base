#!/usr/bin/env python3
"""
Historical Scanner Service
Orchestrates historical scanning of repository commits with progress tracking,
batch processing, and error handling.
"""

import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any, Tuple
from queue import Queue, Empty
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from models import RepositoryConfig, HistoricalScanConfig, HistoricalScanStatus, CommitInfo
from repository_backends import RepositoryBackendManager
from document_processor import DocumentProcessor
from document_database import DocumentDatabase, DocumentRecord
from ollama_client import OllamaClient
from metadata_extractor import MetadataExtractor


@dataclass
class ScanTask:
    """Represents a historical scanning task"""
    repository_id: str
    repository_name: str
    scan_config: HistoricalScanConfig
    revisions_to_scan: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    priority: int = 1  # Lower number = higher priority


@dataclass
class ScanProgress:
    """Progress tracking for a historical scan"""
    repository_id: str
    total_revisions: int
    processed_revisions: int
    failed_revisions: int
    current_revision: Optional[str] = None
    started_at: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None
    error_message: Optional[str] = None
    status: HistoricalScanStatus = HistoricalScanStatus.NOT_STARTED


class HistoricalScanner:
    """Service for orchestrating historical repository scanning"""
    
    def __init__(self, backend_manager: RepositoryBackendManager,
                 document_processor: DocumentProcessor,
                 ollama_client: OllamaClient,
                 max_concurrent_scans: int = 2,
                 db_path: str = "/app/data/documents.db",
                 config_manager=None,
                 monitor_service=None):
        self.backend_manager = backend_manager
        self.document_processor = document_processor
        self.ollama_client = ollama_client
        self.max_concurrent_scans = max_concurrent_scans
        self.config_manager = config_manager
        self.monitor_service = monitor_service

        # Initialize database for direct document storage
        self.document_db = DocumentDatabase(db_path)

        # Initialize metadata extractor
        self.metadata_extractor = MetadataExtractor(ollama_client)

        self.logger = logging.getLogger(__name__)
        
        # Task management
        self.scan_queue: Queue[ScanTask] = Queue()
        self.active_scans: Dict[str, ScanProgress] = {}
        self.completed_scans: Dict[str, ScanProgress] = {}
        
        # Threading
        self.worker_threads: List[threading.Thread] = []
        self.running = False
        self.shutdown_event = threading.Event()
        
        # Progress callbacks
        self.progress_callbacks: List[Callable[[str, ScanProgress], None]] = []
        
        # Statistics
        self.stats = {
            'total_scans_started': 0,
            'total_scans_completed': 0,
            'total_scans_failed': 0,
            'total_revisions_processed': 0,
            'total_documents_generated': 0
        }
    
    def start(self):
        """Start the historical scanner service"""
        if self.running:
            return
        
        self.running = True
        self.shutdown_event.clear()
        
        # Start worker threads
        for i in range(self.max_concurrent_scans):
            thread = threading.Thread(
                target=self._worker_thread,
                name=f"HistoricalScanner-{i}",
                daemon=True
            )
            thread.start()
            self.worker_threads.append(thread)
        
        self.logger.info(f"Historical scanner started with {self.max_concurrent_scans} worker threads")
    
    def stop(self):
        """Stop the historical scanner service"""
        if not self.running:
            return
        
        self.logger.info("Stopping historical scanner...")
        self.running = False
        self.shutdown_event.set()
        
        # Wait for worker threads to finish
        for thread in self.worker_threads:
            thread.join(timeout=30)
        
        self.worker_threads.clear()
        self.logger.info("Historical scanner stopped")
    
    def queue_scan(self, repo: RepositoryConfig, scan_config: HistoricalScanConfig, 
                   priority: int = 1) -> bool:
        """
        Queue a historical scan for a repository
        
        Args:
            repo: Repository configuration
            scan_config: Historical scan configuration
            priority: Scan priority (lower = higher priority)
            
        Returns:
            True if scan was queued successfully
        """
        try:
            # Check if scan is already active
            if repo.id in self.active_scans:
                self.logger.warning(f"Scan already active for repository {repo.name}")
                return False
            
            # Get backend for repository
            backend = self.backend_manager.get_backend_for_repository(repo, None)
            if not backend:
                self.logger.error(f"No backend available for repository {repo.name}")
                return False
            
            # Determine revisions to scan
            revisions_to_scan, error_message = self._determine_revisions_to_scan(repo, scan_config, backend)
            if not revisions_to_scan:
                self.logger.warning(f"No revisions to scan for repository {repo.name}: {error_message}")
                # Store the error message for the UI to display
                if repo.id not in self.completed_scans:
                    progress = ScanProgress(
                        repository_id=repo.id,
                        total_revisions=0,
                        processed_revisions=0,
                        failed_revisions=0,
                        status=HistoricalScanStatus.FAILED,
                        error_message=error_message
                    )
                    self.completed_scans[repo.id] = progress
                return False

            # Check permissions early if documentation generation is enabled
            if scan_config.generate_documentation:
                permission_error = self._check_output_permissions(repo)
                if permission_error:
                    self.logger.error(f"Permission check failed for repository {repo.name}: {permission_error}")
                    # Store the permission error for the UI to display
                    if repo.id not in self.completed_scans:
                        progress = ScanProgress(
                            repository_id=repo.id,
                            total_revisions=0,
                            processed_revisions=0,
                            failed_revisions=0,
                            status=HistoricalScanStatus.FAILED,
                            error_message=permission_error
                        )
                        self.completed_scans[repo.id] = progress
                    return False
            
            # Create scan task
            task = ScanTask(
                repository_id=repo.id,
                repository_name=repo.name,
                scan_config=scan_config,
                revisions_to_scan=revisions_to_scan,
                priority=priority
            )
            
            # Create initial progress tracking and set status to IN_PROGRESS immediately
            progress = ScanProgress(
                repository_id=repo.id,
                total_revisions=len(revisions_to_scan),
                processed_revisions=0,
                failed_revisions=0,
                started_at=datetime.now(),
                status=HistoricalScanStatus.IN_PROGRESS
            )

            # Add to active scans immediately so UI shows "in_progress" status
            self.active_scans[repo.id] = progress
            self._notify_progress_callbacks(repo.id, progress)

            # Queue the task
            self.scan_queue.put(task)
            self.stats['total_scans_started'] += 1

            self.logger.info(f"Queued historical scan for {repo.name} with {len(revisions_to_scan)} revisions (status: IN_PROGRESS)")
            return True
            
        except Exception as e:
            self.logger.error(f"Error queuing scan for {repo.name}: {e}")
            return False
    
    def cancel_scan(self, repository_id: str) -> bool:
        """
        Cancel an active scan
        
        Args:
            repository_id: Repository ID to cancel
            
        Returns:
            True if scan was cancelled
        """
        if repository_id in self.active_scans:
            progress = self.active_scans[repository_id]
            progress.status = HistoricalScanStatus.CANCELLED
            progress.error_message = "Scan cancelled by user"
            
            self.logger.info(f"Cancelled scan for repository {repository_id}")
            return True
        
        return False
    
    def get_scan_progress(self, repository_id: str) -> Optional[ScanProgress]:
        """Get progress for a specific scan"""
        return self.active_scans.get(repository_id) or self.completed_scans.get(repository_id)
    
    def get_all_scan_progress(self) -> Dict[str, ScanProgress]:
        """Get progress for all scans (active and completed)"""
        all_progress = {}
        all_progress.update(self.active_scans)
        all_progress.update(self.completed_scans)
        return all_progress
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get scanner statistics"""
        return {
            **self.stats,
            'active_scans': len(self.active_scans),
            'queued_scans': self.scan_queue.qsize(),
            'completed_scans': len(self.completed_scans)
        }
    
    def add_progress_callback(self, callback: Callable[[str, ScanProgress], None]):
        """Add a progress callback function"""
        self.progress_callbacks.append(callback)
    
    def remove_progress_callback(self, callback: Callable[[str, ScanProgress], None]):
        """Remove a progress callback function"""
        if callback in self.progress_callbacks:
            self.progress_callbacks.remove(callback)
    
    def _determine_revisions_to_scan(self, repo: RepositoryConfig,
                                   scan_config: HistoricalScanConfig,
                                   backend) -> Tuple[List[str], Optional[str]]:
        """Determine which revisions need to be scanned

        Returns:
            Tuple of (revisions_list, error_message)
            If revisions_list is empty, error_message explains why
        """
        try:
            revisions = []

            if scan_config.scan_by_revision:
                # Scan by revision range
                revisions = backend.get_revision_range(
                    repo,
                    scan_config.start_revision,
                    scan_config.end_revision
                )
            elif scan_config.scan_by_date and scan_config.start_date and scan_config.end_date:
                # Scan by date range
                revisions = backend.get_revisions_by_date_range(
                    repo,
                    scan_config.start_date,
                    scan_config.end_date
                )

            if not revisions:
                if scan_config.scan_by_revision:
                    return [], f"No revisions found in range {scan_config.start_revision} to {scan_config.end_revision}. Please check your revision range."
                else:
                    return [], f"No revisions found in date range {scan_config.start_date} to {scan_config.end_date}. Please check your date range."

            # Store original count for better error messages
            original_count = len(revisions)

            # Filter out already scanned revisions if resuming
            if scan_config.last_scanned_revision:
                try:
                    last_scanned = int(scan_config.last_scanned_revision)
                    revisions = [r for r in revisions if int(r) > last_scanned]

                    if not revisions and original_count > 0:
                        return [], (f"All {original_count} revisions in the specified range have already been scanned "
                                  f"(last scanned: revision {last_scanned}). "
                                  f"Use the 'Reset Status' button to re-scan previously processed revisions, "
                                  f"or adjust your revision range to scan newer revisions.")
                except (ValueError, TypeError):
                    pass

            return revisions, None

        except Exception as e:
            error_msg = f"Error determining revisions to scan: {str(e)}"
            self.logger.error(f"{error_msg} for {repo.name}")
            return [], error_msg

    def _check_output_permissions(self, repo: RepositoryConfig) -> Optional[str]:
        """Check if we have write permissions to the output directory

        Returns:
            None if permissions are OK, error message string if there's a problem
        """
        try:
            from pathlib import Path
            import tempfile
            import os

            # Create the expected output directory path
            output_dir = Path("/app/data/output/repositories") / repo.name / "docs"

            # Try to create the directory structure
            try:
                output_dir.mkdir(parents=True, exist_ok=True)
            except PermissionError:
                return (f"Cannot create documentation directory '{output_dir}'. "
                       f"This is a file permission issue. "
                       f"Please check that the application has write access to the data directory. "
                       f"You may need to restart the container or fix directory permissions.")

            # Try to write a test file
            test_file = output_dir / ".permission_test"
            try:
                with open(test_file, 'w') as f:
                    f.write("test")
                # Clean up test file
                test_file.unlink()
                return None  # Permissions are OK
            except PermissionError:
                return (f"Cannot write to documentation directory '{output_dir}'. "
                       f"This is a file permission issue. "
                       f"Please check that the application has write access to the data directory. "
                       f"You may need to restart the container or fix directory permissions.")
            except Exception as e:
                return f"Unexpected error checking permissions: {e}"

        except Exception as e:
            return f"Error checking output permissions: {e}"

    def _worker_thread(self):
        """Worker thread for processing scan tasks"""
        thread_name = threading.current_thread().name
        self.logger.info(f"Historical scanner worker {thread_name} started")

        while self.running and not self.shutdown_event.is_set():
            try:
                # Get next task from queue (with timeout)
                try:
                    task = self.scan_queue.get(timeout=1.0)
                except Empty:
                    continue

                # Process the scan task
                self._process_scan_task(task)

                # Mark task as done
                self.scan_queue.task_done()

            except Exception as e:
                self.logger.error(f"Error in worker thread {thread_name}: {e}")
                time.sleep(1)

        self.logger.info(f"Historical scanner worker {thread_name} stopped")

    def _process_scan_task(self, task: ScanTask):
        """Process a single scan task"""
        repo_id = task.repository_id
        repo_name = task.repository_name

        # Get existing progress tracker (created in queue_scan)
        progress = self.active_scans.get(repo_id)
        if not progress:
            # Fallback: create progress tracker if not found (shouldn't happen)
            progress = ScanProgress(
                repository_id=repo_id,
                total_revisions=len(task.revisions_to_scan),
                processed_revisions=0,
                failed_revisions=0,
                started_at=datetime.now(),
                status=HistoricalScanStatus.IN_PROGRESS
            )
            self.active_scans[repo_id] = progress
            self._notify_progress_callbacks(repo_id, progress)

        try:
            self.logger.info(f"Starting historical scan for {repo_name} ({len(task.revisions_to_scan)} revisions)")

            # Get repository configuration (we need the full config)
            repo_config = self._get_repository_config(repo_id)
            if not repo_config:
                raise Exception(f"Repository configuration not found for {repo_id}")

            # Get backend
            backend = self.backend_manager.get_backend_for_repository(repo_config, None)
            if not backend:
                raise Exception(f"No backend available for repository {repo_name}")

            # Process revisions in batches
            batch_size = task.scan_config.batch_size
            revisions = task.revisions_to_scan

            for i in range(0, len(revisions), batch_size):
                # Check if scan was cancelled
                if progress.status == HistoricalScanStatus.CANCELLED:
                    break

                batch = revisions[i:i + batch_size]
                processed_in_batch = self._process_revision_batch(repo_config, backend, batch, task.scan_config, progress)

                # Progress is now updated individually within _process_revision_batch
                # No need to update here to avoid double-counting

                # Small delay between batches to avoid overwhelming the system
                time.sleep(0.1)

            # Mark scan as completed or failed based on results
            if progress.status != HistoricalScanStatus.CANCELLED:
                if progress.failed_revisions > 0:
                    # Scan had failures (including documentation generation failures)
                    progress.status = HistoricalScanStatus.FAILED
                    progress.error_message = f"Scan completed with {progress.failed_revisions} failed revisions. Check logs for details."
                    self.stats['total_scans_failed'] += 1
                    self.logger.warning(f"Historical scan for {repo_name} completed with failures: "
                                      f"{progress.processed_revisions} processed, {progress.failed_revisions} failed")
                else:
                    # Scan was completely successful
                    progress.status = HistoricalScanStatus.COMPLETED
                    self.stats['total_scans_completed'] += 1
                    self.logger.info(f"Historical scan for {repo_name} completed successfully: "
                                   f"{progress.processed_revisions} processed, {progress.failed_revisions} failed")

                # Find and update the actual repository configuration
                self.logger.info(f"Attempting to save configuration for scan of {repo_name} (status: {progress.status.value})")
                if self.monitor_service:
                    self.logger.info(f"Monitor service is available, looking for repository {repo_id}")
                    try:
                        # Find the repository in the monitor service config
                        repo_config = None
                        for repo in self.monitor_service.config.repositories:
                            if repo.id == repo_id:
                                repo_config = repo
                                break

                        if repo_config and repo_config.historical_scan:
                            # Update the actual repository scan config with the final status
                            repo_config.historical_scan.scan_status = progress.status
                            repo_config.historical_scan.scan_completed_at = datetime.now()
                            repo_config.historical_scan.processed_revisions = progress.processed_revisions
                            repo_config.historical_scan.failed_revisions = progress.failed_revisions
                            repo_config.historical_scan.error_message = progress.error_message

                            # Only update last_scanned_revision if scan was completely successful
                            if progress.status == HistoricalScanStatus.COMPLETED and revisions:
                                try:
                                    repo_config.historical_scan.last_scanned_revision = int(revisions[-1])
                                except (ValueError, TypeError):
                                    # Handle non-numeric revision systems
                                    pass

                            # Save the updated configuration to file
                            self.monitor_service.save_config()
                            self.logger.info(f"Saved configuration after scan for {repo_name}: {progress.status.value}")
                        else:
                            self.logger.warning(f"Could not find repository config for {repo_name} to update scan status")
                    except Exception as e:
                        self.logger.error(f"Error saving configuration after scan: {e}")



        except Exception as e:
            self.logger.error(f"Error processing scan for {repo_name}: {e}")
            progress.status = HistoricalScanStatus.FAILED
            progress.error_message = str(e)
            self.stats['total_scans_failed'] += 1

            # Find and update the actual repository configuration for failure
            if self.monitor_service:
                try:
                    # Find the repository in the monitor service config
                    repo_config = None
                    for repo in self.monitor_service.config.repositories:
                        if repo.id == repo_id:
                            repo_config = repo
                            break

                    if repo_config and repo_config.historical_scan:
                        # Update scan config with failure status
                        repo_config.historical_scan.scan_status = HistoricalScanStatus.FAILED
                        repo_config.historical_scan.error_message = str(e)

                        # Save the updated configuration to file
                        self.monitor_service.save_config()
                        self.logger.debug(f"Saved configuration after scan failure for {repo_name}")
                    else:
                        self.logger.warning(f"Could not find repository config for {repo_name} to update failure status")
                except Exception as save_error:
                    self.logger.error(f"Error saving configuration after scan failure: {save_error}")

        finally:
            # Move from active to completed
            if repo_id in self.active_scans:
                self.completed_scans[repo_id] = self.active_scans.pop(repo_id)

            self._notify_progress_callbacks(repo_id, progress)

    def _process_revision_batch(self, repo_config: RepositoryConfig, backend,
                              revisions: List[str], scan_config: HistoricalScanConfig,
                              progress: ScanProgress):
        """Process a batch of revisions"""
        processed_in_batch = 0
        repo_id = repo_config.id
        last_progress_update = time.time()

        self.logger.info(f"Processing batch of {len(revisions)} revisions for {repo_config.name}: {revisions}")

        for commit_info in backend.get_commit_batch(repo_config, revisions):
            self.logger.info(f"Processing revision {commit_info.revision} for {repo_config.name}")
            try:
                # Check if scan was cancelled
                if progress.status == HistoricalScanStatus.CANCELLED:
                    break

                progress.current_revision = commit_info.revision

                # Skip large commits if configured
                if (scan_config.skip_large_commits and
                    len(commit_info.changed_paths) > scan_config.max_files_per_commit):
                    self.logger.debug(f"Skipping large commit {commit_info.revision} "
                                    f"({len(commit_info.changed_paths)} files)")
                    processed_in_batch += 1
                    progress.processed_revisions += 1

                    # Update progress every 3 revisions or every 2 seconds
                    current_time = time.time()
                    if (processed_in_batch % 3 == 0 or
                        current_time - last_progress_update >= 2.0):
                        self._update_estimated_completion(progress)
                        self._notify_progress_callbacks(repo_id, progress)
                        last_progress_update = current_time
                    continue

                # Generate documentation if enabled
                documentation_success = True
                if scan_config.generate_documentation:
                    documentation_success = self._generate_documentation(commit_info, scan_config, repo_config)
                    if not documentation_success:
                        self.logger.warning(f"Documentation generation failed for revision {commit_info.revision}")
                        progress.failed_revisions += 1
                    else:
                        self.logger.debug(f"Documentation generation succeeded for revision {commit_info.revision}")

                # Only count as successfully processed if documentation generation succeeded (when enabled)
                if documentation_success:
                    self.stats['total_revisions_processed'] += 1
                    processed_in_batch += 1
                    progress.processed_revisions += 1
                else:
                    # Still increment batch counter to maintain progress tracking
                    processed_in_batch += 1

                # Update progress every 3 revisions or every 2 seconds
                current_time = time.time()
                if (processed_in_batch % 3 == 0 or
                    current_time - last_progress_update >= 2.0):
                    self._update_estimated_completion(progress)
                    self._notify_progress_callbacks(repo_id, progress)
                    last_progress_update = current_time

            except PermissionError as e:
                # Handle permission errors specifically - these are critical and should stop the scan
                self.logger.error(f"Permission error processing revision {commit_info.revision}: {e}")
                progress.status = HistoricalScanStatus.FAILED
                progress.error_message = str(e)
                # Don't continue processing if we have permission issues
                break
            except Exception as e:
                self.logger.error(f"Error processing revision {commit_info.revision}: {e}")
                progress.failed_revisions += 1
                processed_in_batch += 1
                progress.processed_revisions += 1

                # Update progress every 3 revisions or every 2 seconds
                current_time = time.time()
                if (processed_in_batch % 3 == 0 or
                    current_time - last_progress_update >= 2.0):
                    self._update_estimated_completion(progress)
                    self._notify_progress_callbacks(repo_id, progress)
                    last_progress_update = current_time

        # Final progress update for this batch
        self._update_estimated_completion(progress)
        self._notify_progress_callbacks(repo_id, progress)

        return processed_in_batch

    def _generate_documentation(self, commit_info: CommitInfo, scan_config: HistoricalScanConfig, repo_config: 'RepositoryConfig') -> bool:
        """Generate documentation for a commit

        Returns:
            True if documentation was successfully generated and saved, False otherwise
        """
        try:
            # Generate documentation using Ollama
            documentation = self.ollama_client.generate_documentation(commit_info)

            # Check if documentation generation was successful
            if documentation and not documentation.startswith("Error:"):
                # Save documentation using the integrated document system
                success = self._save_historical_documentation_integrated(commit_info, documentation, repo_config)

                if success:
                    self.stats['total_documents_generated'] += 1
                    self.logger.debug(f"Successfully generated documentation for revision {commit_info.revision}")
                    return True
                else:
                    self.logger.error(f"Failed to save documentation for revision {commit_info.revision}")
                    return False
            else:
                if documentation and documentation.startswith("Error:"):
                    self.logger.error(f"Ollama error generating documentation for revision {commit_info.revision}: {documentation}")
                else:
                    self.logger.error(f"No documentation generated by Ollama for revision {commit_info.revision}")
                return False

        except PermissionError as e:
            # Handle permission errors specifically
            self.logger.error(f"Permission error generating documentation for revision {commit_info.revision}: {e}")
            raise PermissionError(f"File permission error during documentation generation: {e}")
        except Exception as e:
            self.logger.error(f"Error generating documentation for revision {commit_info.revision}: {e}")
            raise

    def _save_historical_documentation_integrated(self, commit_info: CommitInfo, documentation: str, repo_config: 'RepositoryConfig') -> bool:
        """Save historical documentation using the integrated document system

        Returns:
            True if documentation was successfully saved, False otherwise
        """
        try:
            from pathlib import Path
            from datetime import datetime

            # Create output directory structure (same as existing system)
            output_dir = Path("/app/data/output/repositories") / commit_info.repository_name / "docs"

            # Log directory creation for debugging
            self.logger.debug(f"Creating documentation directory: {output_dir}")
            if self._is_uuid(commit_info.repository_name):
                self.logger.warning(f"Repository name appears to be UUID: {commit_info.repository_name}. "
                                  f"This may indicate a configuration issue.")
            else:
                self.logger.debug(f"Using friendly repository name: {commit_info.repository_name}")

            try:
                output_dir.mkdir(parents=True, exist_ok=True)
            except PermissionError as e:
                raise PermissionError(f"Cannot create documentation directory '{output_dir}'. "
                                    f"This is likely a file permission issue. "
                                    f"Please check that the application has write access to the data directory. "
                                    f"You may need to restart the container or fix directory permissions. "
                                    f"Original error: {e}")

            # Create filename
            filename = f"revision_{commit_info.revision}.md"
            filepath = output_dir / filename

            # Write documentation to file
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(documentation)

                # Verify the file was actually written
                if not (filepath.exists() and filepath.stat().st_size > 0):
                    self.logger.error(f"Documentation file {filepath} was not created or is empty")
                    return False

                self.logger.debug(f"Successfully saved documentation to {filepath}")

            except PermissionError as e:
                raise PermissionError(f"Cannot write documentation file '{filepath}'. "
                                    f"This is a file permission issue. "
                                    f"Please check that the application has write access to the data directory. "
                                    f"You may need to restart the container or fix directory permissions. "
                                    f"Original error: {e}")

            # Get file stats
            file_stat = filepath.stat()

            # Parse metadata from documentation using centralized extractor
            metadata = self.metadata_extractor.extract_all_metadata(documentation)

            # Create document record for database using centralized ID generation
            doc_id = self.metadata_extractor.generate_document_id(commit_info.repository_id, int(commit_info.revision))

            # Parse date using centralized logic with fallbacks
            doc_date = self.metadata_extractor.parse_date_with_fallbacks(commit_info.date)

            # Handle commit message - use AI-generated summary if no original message
            commit_message = commit_info.message
            if not commit_info.message or commit_info.message.strip() == "":
                # Extract AI-generated summary from the documentation using centralized extractor
                ai_summary = self.metadata_extractor.extract_ai_summary(documentation)
                if ai_summary:
                    commit_message = f"{ai_summary} (AI-generated summary)"
                    self.logger.debug(f"Using AI-generated summary as commit message for revision {commit_info.revision}")
                else:
                    commit_message = "No commit message available"

            doc_record = DocumentRecord(
                id=doc_id,
                repository_id=commit_info.repository_id,
                repository_name=commit_info.repository_name,
                revision=int(commit_info.revision),
                date=doc_date,
                filename=filename,
                filepath=str(filepath),
                size=file_stat.st_size,
                author=commit_info.author,
                commit_message=commit_message,
                changed_paths=commit_info.changed_paths,  # Store the list of changed files
                code_review_recommended=metadata.get('code_review_recommended'),
                code_review_priority=metadata.get('code_review_priority'),
                documentation_impact=metadata.get('documentation_impact'),
                risk_level=metadata.get('risk_level'),
                file_modified_time=file_stat.st_mtime,
                processed_time=datetime.now(),
                repository_url=repo_config.url,  # Store repository URL for diff recreation
                repository_type='svn'  # SVN repository type for diff generation
            )

            # Save to database
            if self.document_db.upsert_document(doc_record):
                self.logger.debug(f"Saved historical documentation to database: {doc_id}")

                # Trigger cache invalidation in document processor to ensure immediate visibility
                try:
                    self.document_processor.processed_files.add(str(filepath))
                except Exception as e:
                    self.logger.debug(f"Could not update document processor cache: {e}")

                # CRITICAL: Invalidate DocumentService cache to ensure web interface shows new documents
                # Note: We need to invalidate the cache of the DocumentService instance used by the web interface
                # Since we can't easily access that instance, we'll use a file-based cache invalidation signal
                try:
                    cache_signal_file = Path("/app/data/cache_invalidate_signal")
                    cache_signal_file.touch()
                    self.logger.debug(f"Created cache invalidation signal for new document: {doc_id}")
                except Exception as e:
                    self.logger.warning(f"Could not create cache invalidation signal: {e}")

                return True  # Successfully saved file and database record
            else:
                self.logger.warning(f"Failed to save document to database: {doc_id}")
                # File was saved but database failed - still consider this a partial success
                return True

        except Exception as e:
            self.logger.error(f"Error saving historical documentation for revision {commit_info.revision}: {e}")
            return False



    def _extract_document_metadata(self, documentation: str) -> Dict[str, Any]:
        """Extract metadata from generated documentation using hybrid heuristic + LLM approach"""
        metadata: Dict[str, Any] = {}

        try:
            # First try heuristic extraction (fast)
            code_review_rec = self._extract_code_review_recommendation(documentation)
            if code_review_rec is not None:
                metadata['code_review_recommended'] = code_review_rec

                # Extract priority if review is recommended
                if code_review_rec:
                    priority = self._extract_code_review_priority(documentation)
                    if priority:
                        metadata['code_review_priority'] = priority

            # Extract documentation impact using robust section-based approach
            doc_impact = self._extract_documentation_impact(documentation)
            if doc_impact is not None:
                metadata['documentation_impact'] = doc_impact

            # Extract risk level using robust section-based approach
            risk_level = self._extract_risk_level(documentation)
            if risk_level:
                metadata['risk_level'] = risk_level

            # Check if any critical fields are missing and use LLM fallback
            missing_fields = []
            if metadata.get('code_review_recommended') is None:
                missing_fields.append('code_review_recommended')
            if metadata.get('code_review_priority') is None and metadata.get('code_review_recommended'):
                missing_fields.append('code_review_priority')
            if metadata.get('risk_level') is None:
                missing_fields.append('risk_level')

            if missing_fields:
                self.logger.info(f"Heuristic extraction incomplete for {missing_fields}, using LLM fallback")
                llm_metadata = self._extract_metadata_with_llm(documentation)

                # Fill in missing values with LLM results
                for field in missing_fields:
                    if llm_metadata.get(field) is not None:
                        metadata[field] = llm_metadata[field]
                        self.logger.debug(f"LLM provided {field}: {llm_metadata[field]}")

        except Exception as e:
            self.logger.error(f"Error extracting document metadata: {e}")

        return metadata

    def _extract_metadata_with_llm(self, documentation: str) -> Dict[str, Any]:
        """Extract metadata using LLM when heuristics fail"""
        try:
            system_prompt = """You are a metadata extraction assistant. Analyze the provided technical documentation and extract specific metadata fields.

Return ONLY a JSON object with these exact fields:
- code_review_recommended: true/false (whether code review is recommended)
- code_review_priority: "HIGH"/"MEDIUM"/"LOW" (priority level if review is recommended, null if not recommended)
- risk_level: "HIGH"/"MEDIUM"/"LOW" (risk level of the changes)
- documentation_impact: true/false (whether documentation needs updates)

Be precise and consistent. If you cannot determine a value, use null."""

            prompt = f"""Analyze this technical documentation and extract the metadata:

{documentation}

Return only the JSON object with the metadata fields."""

            response = self.ollama_client.call_ollama(prompt, system_prompt)

            if response:
                # Try to parse JSON response
                import json
                try:
                    # Extract JSON from response (LLM might wrap it in explanatory text)
                    json_str = response.strip()

                    # Look for JSON object in the response
                    start_idx = json_str.find('{')
                    end_idx = json_str.rfind('}')

                    if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                        json_str = json_str[start_idx:end_idx+1]

                    metadata = json.loads(json_str)

                    # Validate and normalize the response
                    normalized = {}

                    # Code review recommendation
                    if 'code_review_recommended' in metadata:
                        val = metadata['code_review_recommended']
                        if isinstance(val, bool):
                            normalized['code_review_recommended'] = val
                        elif isinstance(val, str):
                            normalized['code_review_recommended'] = val.lower() in ['true', 'yes', '1']

                    # Code review priority
                    if 'code_review_priority' in metadata:
                        val = metadata['code_review_priority']
                        if val and isinstance(val, str) and val.upper() in ['HIGH', 'MEDIUM', 'LOW']:
                            normalized['code_review_priority'] = val.upper()

                    # Risk level
                    if 'risk_level' in metadata:
                        val = metadata['risk_level']
                        if val and isinstance(val, str) and val.upper() in ['HIGH', 'MEDIUM', 'LOW']:
                            normalized['risk_level'] = val.upper()

                    # Documentation impact
                    if 'documentation_impact' in metadata:
                        val = metadata['documentation_impact']
                        if isinstance(val, bool):
                            normalized['documentation_impact'] = val
                        elif isinstance(val, str):
                            normalized['documentation_impact'] = val.lower() in ['true', 'yes', '1']

                    self.logger.debug(f"LLM extracted metadata: {normalized}")
                    return normalized

                except json.JSONDecodeError as e:
                    self.logger.warning(f"Failed to parse LLM JSON response: {e}")
                    self.logger.debug(f"Raw LLM response: {response}")

            return {}

        except Exception as e:
            self.logger.error(f"Error extracting metadata with LLM: {e}")
            return {}

    def _extract_code_review_recommendation(self, content: str) -> Optional[bool]:
        """Extract code review recommendation from LLM analysis"""
        try:
            # Look for code review recommendation section
            review_section = self._extract_section(content, "Code Review Recommendation")
            if not review_section:
                return None

            review_lower = review_section.lower()
            if ("not required" in review_lower or "no review" in review_lower or "skip review" in review_lower or
                "does not require" in review_lower or "should not require" in review_lower or
                "not be subject to" in review_lower or "not necessary" in review_lower):
                return False
            elif ("recommended" in review_lower or "should be reviewed" in review_lower or
                  "should be code reviewed" in review_lower or "requires review" in review_lower or
                  "should be considered" in review_lower or "consider" in review_lower or
                  "priority" in review_lower):
                return True

            return None
        except Exception:
            return None

    def _extract_code_review_priority(self, content: str) -> Optional[str]:
        """Extract code review priority from LLM analysis with LLM fallback"""
        try:
            # First try heuristic extraction
            review_section = self._extract_section(content, "Code Review Recommendation")
            if review_section:
                review_lower = review_section.lower()

                # Explicit priority mentions
                if "high priority" in review_lower or "urgent" in review_lower or "critical" in review_lower:
                    return "HIGH"
                elif "medium priority" in review_lower or "moderate" in review_lower:
                    return "MEDIUM"
                elif "low priority" in review_lower or "optional" in review_lower:
                    return "LOW"

                # Infer priority from context (check negative indicators first)
                if ("may not require" in review_lower or "simple" in review_lower or
                    "straightforward" in review_lower or "minimal" in review_lower or
                    "quick glance" in review_lower or "due to its simplicity" in review_lower):
                    return "LOW"
                elif ("extensive review" in review_lower or "thorough" in review_lower or
                      "careful" in review_lower or "detailed" in review_lower):
                    return "HIGH"
                else:
                    return "MEDIUM"  # Default to medium if review is recommended but no clear priority

            # If heuristic failed and we have LLM client, try LLM fallback
            if self.ollama_client:
                self.logger.debug("Heuristic priority extraction failed, trying LLM fallback")
                llm_result = self._extract_metadata_with_llm(content)
                return llm_result.get('code_review_priority')

            return None
        except Exception as e:
            self.logger.debug(f"Error extracting code review priority: {e}")
            return None

    def _extract_documentation_impact(self, content: str) -> Optional[bool]:
        """Extract documentation impact from LLM analysis with LLM fallback"""
        try:
            # First try heuristic extraction
            doc_section = self._extract_section(content, "Documentation Impact")
            if doc_section:
                doc_lower = doc_section.lower()
                if ("not required" in doc_lower or "no updates" in doc_lower or "no impact" in doc_lower or
                    "no documentation updates" in doc_lower):
                    return False
                elif "required" in doc_lower or "should be updated" in doc_lower or "needs update" in doc_lower:
                    return True

            # If heuristic failed and we have LLM client, try LLM fallback
            if self.ollama_client:
                self.logger.debug("Heuristic documentation impact extraction failed, trying LLM fallback")
                llm_result = self._extract_metadata_with_llm(content)
                return llm_result.get('documentation_impact')

            return None
        except Exception:
            return None

    def _extract_risk_level(self, content: str) -> Optional[str]:
        """Extract risk level from LLM analysis with LLM fallback"""
        try:
            # First try heuristic extraction
            sections = ["Code Review Recommendation", "Impact Assessment", "Summary", "Recommendations"]

            for section_name in sections:
                section = self._extract_section(content, section_name)
                if section:
                    section_lower = section.lower()

                    # Explicit risk level mentions
                    if "high risk" in section_lower or "risk level: high" in section_lower:
                        return "HIGH"
                    elif "medium risk" in section_lower or "risk level: medium" in section_lower:
                        return "MEDIUM"
                    elif "low risk" in section_lower or "risk level: low" in section_lower:
                        return "LOW"

                    # Infer risk from context
                    if ("security risks" in section_lower or "complex technical dependencies" in section_lower or
                        "breaking changes" in section_lower or "critical" in section_lower):
                        # Check if it says "no security risks" or "does not introduce"
                        if ("no security risks" in section_lower or "does not introduce" in section_lower or
                            "no additional" in section_lower):
                            return "LOW"
                        else:
                            return "HIGH"
                    elif ("minimal impact" in section_lower or "straightforward" in section_lower or
                          "simple" in section_lower or "no additional follow-up" in section_lower):
                        return "LOW"

            # If heuristic extraction found some indicators but no definitive result, try LLM fallback
            if self.ollama_client:
                self.logger.debug("Heuristic risk level extraction inconclusive, trying LLM fallback")
                llm_result = self._extract_metadata_with_llm(content)
                risk_level = llm_result.get('risk_level')
                if risk_level:
                    return risk_level

            return "MEDIUM"  # Default to medium if no clear indicators

        except Exception as e:
            self.logger.debug(f"Error extracting risk level: {e}")
            return None

    def _extract_section(self, content: str, section_name: str) -> Optional[str]:
        """Extract a specific section from the document content"""
        try:
            # Look for markdown section headers
            import re
            pattern = rf'##\s*{re.escape(section_name)}\s*\n(.*?)(?=\n##|\Z)'
            match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
            return match.group(1).strip() if match else None
        except Exception:
            return None

    def _extract_ai_summary_from_documentation(self, documentation: str) -> Optional[str]:
        """Extract AI-generated summary from documentation content to use as commit message"""
        try:
            # Look for the Summary section in the documentation
            summary_content = self._extract_section(documentation, "Summary")

            if summary_content:
                # Clean up the summary - take first sentence or first line if it's concise
                lines = summary_content.split('\n')
                first_line = lines[0].strip()

                # If first line is a complete sentence and not too long, use it
                if first_line and len(first_line) <= 100 and (first_line.endswith('.') or len(lines) == 1):
                    return first_line.rstrip('.')

                # Otherwise, try to find a concise summary sentence
                for line in lines:
                    line = line.strip()
                    if line and len(line) <= 100 and not line.startswith('*') and not line.startswith('-'):
                        return line.rstrip('.')

                # Fallback: use first 80 characters of summary
                if len(first_line) > 0:
                    return (first_line[:80] + '...') if len(first_line) > 80 else first_line.rstrip('.')

            return None
        except Exception as e:
            self.logger.debug(f"Error extracting AI summary from documentation: {e}")
            return None

    def _get_repository_config(self, repo_id: str) -> Optional[RepositoryConfig]:
        """Get repository configuration by ID"""
        try:
            # Get the actual repository configuration from the config manager
            if hasattr(self, 'config_manager') and self.config_manager:
                config = self.config_manager.load_config()
                for repo in config.repositories:
                    if repo.id == repo_id:
                        return repo

            # Fallback: try to get from monitor service if available
            if hasattr(self, 'monitor_service') and self.monitor_service:
                for repo in self.monitor_service.config.repositories:
                    if repo.id == repo_id:
                        return repo

            self.logger.error(f"Repository configuration not found for {repo_id}")
            return None

        except Exception as e:
            self.logger.error(f"Error getting repository config for {repo_id}: {e}")
            return None

    def _is_uuid(self, value: str) -> bool:
        """Check if a string is a valid UUID"""
        try:
            import uuid
            uuid.UUID(value)
            return True
        except (ValueError, AttributeError):
            return False

    def _update_estimated_completion(self, progress: ScanProgress):
        """Update estimated completion time based on current progress"""
        if progress.started_at and progress.processed_revisions > 0:
            elapsed = datetime.now() - progress.started_at
            rate = progress.processed_revisions / elapsed.total_seconds()

            if rate > 0:
                remaining_revisions = progress.total_revisions - progress.processed_revisions
                remaining_seconds = remaining_revisions / rate
                progress.estimated_completion = datetime.now() + timedelta(seconds=remaining_seconds)

    def _notify_progress_callbacks(self, repo_id: str, progress: ScanProgress):
        """Notify all registered progress callbacks"""
        for callback in self.progress_callbacks:
            try:
                callback(repo_id, progress)
            except Exception as e:
                self.logger.error(f"Error in progress callback: {e}")
