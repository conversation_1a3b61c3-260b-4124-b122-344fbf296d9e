# RepoSense AI Environment Configuration Template
#
# ⚠️  IMPORTANT: Environment variables are now OPTIONAL!
#
# RepoSense AI now uses the web interface for primary configuration.
# Only use environment variables for deployment overrides.
#
# Most users should configure via the web interface at http://localhost:5000
# and leave this file empty or use it only for deployment-specific overrides.

# =============================================================================
# DEPLOYMENT OVERRIDES (Optional)
# =============================================================================

# Uncomment only if you need to override web interface configuration:

# Ollama server URL override:
# OLLAMA_BASE_URL=http://ollama:11434

# Ollama model override:
# OLLAMA_MODEL=qwen3

# Web interface overrides (for deployment):
# REPOSENSE_AI_WEB_HOST=0.0.0.0
# REPOSENSE_AI_WEB_PORT=5000

# =============================================================================
# WEB INTERFACE CONFIGURATION (Optional)
# =============================================================================

# Web interface host (usually 0.0.0.0 for Docker)
REPOSENSE_AI_WEB_HOST=0.0.0.0

# Web interface port
REPOSENSE_AI_WEB_PORT=5000

# =============================================================================
# APPLICATION CONFIGURATION (Optional)
# =============================================================================

# Environment type
REPOSENSE_AI_ENV=development

# Python settings
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# Flask settings (for development)
FLASK_ENV=development
FLASK_DEBUG=1

# Log level
REPOSENSE_AI_LOG_LEVEL=DEBUG

# =============================================================================
# NOTES
# =============================================================================

# Priority Order:
# 1. Environment Variables (this file) - HIGHEST PRIORITY
# 2. Configuration File (config.json)
# 3. Application Defaults - LOWEST PRIORITY

# Environment variables will ALWAYS override config file values.
# This is intentional for deployment flexibility.

# To check your effective configuration, run:
# python config_summary.py
