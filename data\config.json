{"users": [], "repositories": [{"id": "bafcf65b-ed52-455c-9296-0936037bc39b", "name": "reposense_ai", "url": "http://sundc:81/svn/reposense_ai", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 28, "last_commit_date": "2025-08-10T20:46:20.904286", "last_processed_time": "2025-08-10T23:44:03.747802", "enabled": true, "branch_path": null, "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": true, "scan_by_revision": true, "start_revision": 1, "end_revision": 28, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "last_scanned_revision": 28, "scan_status": "completed", "scan_started_at": null, "scan_completed_at": "2025-08-10T23:51:21.349297", "total_revisions": 28, "processed_revisions": 28, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": []}, {"id": "d5d13d3f-7511-4e99-a5ec-f68122980bd1", "name": "svn_monitor_server_test_repo", "url": "http://sundc:81/svn/svn_monitor_server_test_repo", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 8, "last_commit_date": "2025-08-06T19:49:43.657025", "last_processed_time": "2025-08-10T23:53:34.440007", "enabled": true, "branch_path": null, "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": true, "scan_by_revision": true, "start_revision": 1, "end_revision": 8, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "last_scanned_revision": 8, "scan_status": "completed", "scan_started_at": null, "scan_completed_at": "2025-08-10T23:56:26.303777", "total_revisions": null, "processed_revisions": 8, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": ["/CHANGELOG.docx", "/CHANGELOG.html", "/README.md"]}], "ollama_host": "http://************:11434", "ollama_model": "granite3.3:8b", "ollama_model_documentation": null, "ollama_model_code_review": null, "ollama_model_risk_assessment": null, "ollama_timeout_base": 180, "ollama_timeout_connection": 30, "ollama_timeout_embeddings": 60, "use_enhanced_prompts": true, "enhanced_prompts_fallback": true, "check_interval": 300, "svn_server_url": "http://sundc:81/svn", "svn_server_username": "fvaneijk", "svn_server_password": "an<PERSON><PERSON><PERSON>", "svn_server_type": "auto", "smtp_host": "localhost", "smtp_port": 587, "smtp_username": null, "smtp_password": null, "email_from": "<EMAIL>", "email_recipients": [], "output_dir": "/app/data/output", "generate_docs": true, "send_emails": false, "web_enabled": true, "web_port": 5000, "web_host": "0.0.0.0", "web_secret_key": "82f903c649d77c01068f13c50ec49cc60715d42f6ed902d71501da8b63bf6f15", "web_log_entries": 300}