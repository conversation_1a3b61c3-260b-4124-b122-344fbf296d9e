#!/usr/bin/env python3
"""
Diff Service - Generate SCM diff content on-demand
"""

import logging
import subprocess
from typing import Optional
from document_database import DocumentRecord

class DiffService:
    """Service for generating SCM diff content on-demand"""

    def __init__(self, config_manager=None):
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
    
    def get_diff_for_document(self, document: DocumentRecord, format_type: str = 'unified') -> Optional[str]:
        """
        Generate diff content for a document based on its repository metadata

        Args:
            document: DocumentRecord containing repository metadata
            format_type: 'unified' or 'side-by-side'

        Returns:
            Diff content as string, or None if unable to generate
        """
        # Get repository metadata (from document or infer from configuration)
        repo_url = document.repository_url
        repo_type = document.repository_type

        if not repo_url or not repo_type:
            # Try to infer from configuration
            try:
                config = self.config_manager.load_config()
                for repo in config.repositories:
                    if repo.id == document.repository_id:
                        repo_url = repo.url
                        repo_type = getattr(repo, 'type', 'svn')
                        self.logger.info(f"Inferred repository metadata for {document.id}: {repo_type} - {repo_url}")
                        break

                if not repo_url or not repo_type:
                    self.logger.warning(f"Document {document.id} missing repository metadata and cannot infer from config")
                    return None

            except Exception as e:
                self.logger.error(f"Error inferring repository metadata: {e}")
                return None

        try:
            if repo_type.lower() == 'svn':
                # Create a temporary document with the inferred metadata for SVN operations
                temp_document = document
                if not document.repository_url:
                    # We need to pass the inferred URL to the SVN diff method
                    # For now, we'll modify the document temporarily
                    temp_document.repository_url = repo_url
                    temp_document.repository_type = repo_type
                unified_diff = self._get_svn_diff(temp_document)
            elif repo_type.lower() == 'git':
                # Create a temporary document with the inferred metadata for Git operations
                temp_document = document
                if not document.repository_url:
                    temp_document.repository_url = repo_url
                    temp_document.repository_type = repo_type
                unified_diff = self._get_git_diff(temp_document)
            else:
                self.logger.error(f"Unsupported repository type: {repo_type}")
                return None

            if not unified_diff:
                return None

            # Convert to requested format
            if format_type == 'side-by-side':
                return self._convert_to_side_by_side(unified_diff)
            else:
                return unified_diff

        except Exception as e:
            self.logger.error(f"Error generating diff for document {document.id}: {e}")
            return None
    


    def _get_svn_diff(self, document: DocumentRecord) -> Optional[str]:
        """Generate SVN diff for a specific revision using repository credentials"""
        try:
            # Check if repository URL is available
            if not document.repository_url:
                return "Error: Repository URL not available in document metadata."

            # Get repository configuration with credentials
            repo_config = self._get_repository_config(document.repository_url)
            if not repo_config:
                return "Error: Repository configuration not found. Cannot access SVN credentials."

            # For SVN, we need to get the diff between revision-1 and revision
            prev_revision = document.revision - 1
            current_revision = document.revision

            # Build SVN command with credentials
            if prev_revision < 1:
                # For first revision, show the commit as a diff
                cmd = [
                    'svn', 'diff',
                    '--change', str(current_revision),
                    '--non-interactive',
                    '--trust-server-cert'
                ]
                if repo_config.get('username'):
                    cmd.extend(['--username', repo_config['username']])
                if repo_config.get('password'):
                    cmd.extend(['--password', repo_config['password']])
                cmd.append(document.repository_url)
            else:
                # Normal diff between revisions using -r flag
                cmd = [
                    'svn', 'diff',
                    '-r', f'{prev_revision}:{current_revision}',
                    '--non-interactive',
                    '--trust-server-cert'
                ]
                if repo_config.get('username'):
                    cmd.extend(['--username', repo_config['username']])
                if repo_config.get('password'):
                    cmd.extend(['--password', repo_config['password']])
                cmd.append(document.repository_url)

            # Filter out None values and ensure all elements are strings
            cmd = [str(c) for c in cmd if c is not None]

            self.logger.debug(f"Running SVN diff command with credentials: {' '.join([c if c != repo_config.get('password', '') else '***' for c in cmd])}")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=False,  # Get bytes to handle encoding ourselves
                timeout=30  # 30 second timeout
            )

            if result.returncode == 0:
                # Decode output with proper encoding handling
                diff_content = self._decode_svn_output(result.stdout)
                if diff_content.strip():
                    return diff_content
                else:
                    return "No differences found or binary files only"
            else:
                # Decode error message with proper encoding handling
                error_msg = self._decode_svn_output(result.stderr) if result.stderr else "Unknown SVN error"
                self.logger.error(f"SVN diff with credentials failed: {error_msg}")
                return f"Error generating SVN diff: {error_msg}"

        except subprocess.TimeoutExpired:
            self.logger.error(f"SVN diff timed out for document {document.id}")
            return "Error: SVN diff operation timed out"
        except Exception as e:
            self.logger.error(f"Error running SVN diff with credentials: {e}")
            return f"Error running SVN diff: {str(e)}"

    def _decode_svn_output(self, output_bytes: bytes) -> str:
        """
        Decode SVN output bytes with proper encoding handling

        Args:
            output_bytes: Raw bytes from SVN command

        Returns:
            Decoded string, with fallback handling for encoding issues
        """
        if not output_bytes:
            return ""

        # Try different encodings in order of preference
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']

        for encoding in encodings:
            try:
                decoded = output_bytes.decode(encoding)

                # Check if this looks like binary content
                if self._is_likely_binary_content(decoded):
                    return "Binary file content detected - diff not available for binary files"

                return decoded
            except (UnicodeDecodeError, UnicodeError):
                continue

        # If all encodings fail, use error handling
        try:
            return output_bytes.decode('utf-8', errors='replace')
        except Exception:
            return "Error: Unable to decode SVN output - possibly binary content"

    def _is_likely_binary_content(self, content: str) -> bool:
        """
        Check if content appears to be binary based on common indicators

        Args:
            content: Decoded string content

        Returns:
            True if content appears to be binary
        """
        if not content:
            return False

        # Check for high ratio of non-printable characters
        printable_chars = sum(1 for c in content if c.isprintable() or c in '\n\r\t')
        total_chars = len(content)

        if total_chars > 0:
            printable_ratio = printable_chars / total_chars
            if printable_ratio < 0.7:  # Less than 70% printable characters
                return True

        # Check for common binary file indicators
        binary_indicators = [
            b'\x00',  # Null bytes
            b'\xFF\xFE',  # UTF-16 BOM
            b'\xFE\xFF',  # UTF-16 BE BOM
            b'\xEF\xBB\xBF',  # UTF-8 BOM
        ]

        content_bytes = content.encode('utf-8', errors='ignore')
        for indicator in binary_indicators:
            if indicator in content_bytes:
                return True

        return False

    def _get_repository_config(self, repository_url: str) -> Optional[dict]:
        """Get repository configuration including credentials"""
        try:
            if not self.config_manager:
                self.logger.warning("No config manager available for repository credentials")
                return None

            config = self.config_manager.load_config()
            repositories = config.repositories

            self.logger.debug(f"Looking for repository URL: {repository_url}")
            self.logger.debug(f"Available repositories: {[r.url for r in repositories]}")

            # Find repository by URL
            for repo in repositories:
                if repo.url == repository_url:
                    self.logger.debug(f"Found repository config with username: {repo.username or 'None'}")
                    # Convert RepositoryConfig to dict for compatibility
                    return {
                        'url': repo.url,
                        'username': repo.username,
                        'password': repo.password,
                        'name': repo.name,
                        'id': repo.id
                    }

            self.logger.warning(f"Repository configuration not found for URL: {repository_url}")
            return None

        except Exception as e:
            self.logger.error(f"Error getting repository configuration: {e}")
            return None
    
    def _get_git_diff(self, document: DocumentRecord) -> Optional[str]:
        """Generate Git diff for a specific revision"""
        try:
            # For Git, we need to get the diff for the specific commit
            # This would require cloning/accessing the git repository
            # For now, return a placeholder
            self.logger.warning("Git diff generation not yet implemented")
            return "Git diff generation not yet implemented"
            
        except Exception as e:
            self.logger.error(f"Error running Git diff: {e}")
            return None
    
    def can_generate_diff(self, document: DocumentRecord) -> bool:
        """
        Check if we can generate diff for this document

        Args:
            document: DocumentRecord to check

        Returns:
            True if diff can be generated, False otherwise
        """
        # If repository metadata is available, use it
        if (document.repository_url is not None and
            document.repository_type is not None and
            document.repository_type.lower() in ['svn', 'git']):
            return True

        # If metadata is missing, try to infer from configuration
        try:
            config = self.config_manager.load_config()
            for repo in config.repositories:
                if repo.id == document.repository_id:
                    # Found matching repository in config
                    repo_type = getattr(repo, 'type', 'svn')  # Default to SVN
                    return repo_type.lower() in ['svn', 'git']

            # If repository not found in config but we have a repository_id,
            # assume it's SVN (most common case)
            if document.repository_id:
                self.logger.info(f"Repository metadata missing for {document.repository_id}, assuming SVN")
                return True

        except Exception as e:
            self.logger.debug(f"Error checking repository configuration: {e}")

        return False

    def _convert_to_side_by_side(self, unified_diff: str) -> str:
        """Convert unified diff to side-by-side HTML format"""
        try:
            import re

            lines = unified_diff.split('\n')
            html_parts = []

            # Start HTML structure
            html_parts.append('''
<div class="side-by-side-diff">
    <style>
        .side-by-side-diff {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        .diff-header {
            background-color: #f8f9fa;
            padding: 8px 12px;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }
        .diff-table {
            width: 100%;
            border-collapse: collapse;
        }
        .diff-table td {
            padding: 2px 8px;
            vertical-align: top;
            border: none;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .line-number {
            background-color: #f8f9fa;
            color: #666;
            text-align: right;
            width: 40px;
            border-right: 1px solid #ddd;
            user-select: none;
        }
        .line-added {
            background-color: #d4edda;
            color: #155724;
        }
        .line-removed {
            background-color: #f8d7da;
            color: #721c24;
        }
        .line-context {
            background-color: #fff;
            color: #333;
        }
        .line-info {
            background-color: #d1ecf1;
            color: #0c5460;
            font-weight: bold;
        }
    </style>
    <table class="diff-table">
''')

            current_file = None
            left_line_num = 0
            right_line_num = 0

            for line in lines:
                if line.startswith('Index: ') or line.startswith('==='):
                    if line.startswith('Index: '):
                        current_file = line[7:]  # Remove "Index: "
                        html_parts.append(f'<tr><td colspan="4" class="diff-header">📄 {current_file}</td></tr>')
                elif line.startswith('@@'):
                    # Parse hunk header like "@@ -1,4 +1,6 @@"
                    match = re.match(r'@@ -(\d+),?\d* \+(\d+),?\d* @@', line)
                    if match:
                        left_line_num = int(match.group(1))
                        right_line_num = int(match.group(2))
                    html_parts.append(f'<tr><td colspan="4" class="line-info">{line}</td></tr>')
                elif line.startswith('---') or line.startswith('+++'):
                    # Skip these lines as they're redundant with Index
                    continue
                elif line.startswith('-'):
                    # Removed line
                    content = line[1:]  # Remove the '-' prefix
                    html_parts.append(f'''
<tr>
    <td class="line-number">{left_line_num}</td>
    <td class="line-removed">{self._escape_html(content)}</td>
    <td class="line-number"></td>
    <td class="line-context"></td>
</tr>''')
                    left_line_num += 1
                elif line.startswith('+'):
                    # Added line
                    content = line[1:]  # Remove the '+' prefix
                    html_parts.append(f'''
<tr>
    <td class="line-number"></td>
    <td class="line-context"></td>
    <td class="line-number">{right_line_num}</td>
    <td class="line-added">{self._escape_html(content)}</td>
</tr>''')
                    right_line_num += 1
                elif line.startswith(' ') or (line and not line.startswith(('Index:', '===', '---', '+++', '@@'))):
                    # Context line
                    content = line[1:] if line.startswith(' ') else line
                    html_parts.append(f'''
<tr>
    <td class="line-number">{left_line_num}</td>
    <td class="line-context">{self._escape_html(content)}</td>
    <td class="line-number">{right_line_num}</td>
    <td class="line-context">{self._escape_html(content)}</td>
</tr>''')
                    left_line_num += 1
                    right_line_num += 1

            # Close HTML structure
            html_parts.append('    </table>\n</div>')

            return ''.join(html_parts)

        except Exception as e:
            self.logger.error(f"Error converting diff to side-by-side: {e}")
            return f"Error converting diff format: {str(e)}"

    def _escape_html(self, text: str) -> str:
        """Escape HTML characters in text"""
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;'))
