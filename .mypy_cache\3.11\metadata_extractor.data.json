{".class": "MypyFile", "_fullname": "metadata_extractor", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DocumentRecord": {".class": "SymbolTableNode", "cross_ref": "document_database.DocumentRecord", "kind": "Gdef"}, "MetadataExtractor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "metadata_extractor.MetadataExtractor", "name": "MetadataExtractor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "metadata_extractor.MetadataExtractor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "metadata_extractor", "mro": ["metadata_extractor.MetadataExtractor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "ollama_client", "config_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "metadata_extractor.MetadataExtractor.__init__", "name": "__init__", "type": null}}, "_extract_metadata_with_llm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "documentation", "document_record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "metadata_extractor.MetadataExtractor._extract_metadata_with_llm", "name": "_extract_metadata_with_llm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "documentation", "document_record"], "arg_types": ["metadata_extractor.MetadataExtractor", "builtins.str", {".class": "UnionType", "items": ["document_database.DocumentRecord", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_metadata_with_llm of MetadataExtractor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_basic_metadata_prompts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "documentation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "metadata_extractor.MetadataExtractor._get_basic_metadata_prompts", "name": "_get_basic_metadata_prompts", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "documentation"], "arg_types": ["metadata_extractor.MetadataExtractor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_basic_metadata_prompts of MetadataExtractor", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "metadata_extractor.MetadataExtractor.config_manager", "name": "config_manager", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "extract_ai_summary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "metadata_extractor.MetadataExtractor.extract_ai_summary", "name": "extract_ai_summary", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["metadata_extractor.MetadataExtractor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_ai_summary of MetadataExtractor", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_all_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "documentation", "document_record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "metadata_extractor.MetadataExtractor.extract_all_metadata", "name": "extract_all_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "documentation", "document_record"], "arg_types": ["metadata_extractor.MetadataExtractor", "builtins.str", {".class": "UnionType", "items": ["document_database.DocumentRecord", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_all_metadata of MetadataExtractor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_code_review_priority": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "content", "document_record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "metadata_extractor.MetadataExtractor.extract_code_review_priority", "name": "extract_code_review_priority", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "content", "document_record"], "arg_types": ["metadata_extractor.MetadataExtractor", "builtins.str", {".class": "UnionType", "items": ["document_database.DocumentRecord", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_code_review_priority of MetadataExtractor", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_code_review_recommendation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "content", "document_record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "metadata_extractor.MetadataExtractor.extract_code_review_recommendation", "name": "extract_code_review_recommendation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "content", "document_record"], "arg_types": ["metadata_extractor.MetadataExtractor", "builtins.str", {".class": "UnionType", "items": ["document_database.DocumentRecord", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_code_review_recommendation of MetadataExtractor", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_documentation_impact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "content", "document_record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "metadata_extractor.MetadataExtractor.extract_documentation_impact", "name": "extract_documentation_impact", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "content", "document_record"], "arg_types": ["metadata_extractor.MetadataExtractor", "builtins.str", {".class": "UnionType", "items": ["document_database.DocumentRecord", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_documentation_impact of MetadataExtractor", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_field": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "metadata_extractor.MetadataExtractor.extract_field", "name": "extract_field", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "field_name"], "arg_types": ["metadata_extractor.MetadataExtractor", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_field of MetadataExtractor", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_risk_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "content", "document_record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "metadata_extractor.MetadataExtractor.extract_risk_level", "name": "extract_risk_level", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "content", "document_record"], "arg_types": ["metadata_extractor.MetadataExtractor", "builtins.str", {".class": "UnionType", "items": ["document_database.DocumentRecord", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_risk_level of MetadataExtractor", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "section_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "metadata_extractor.MetadataExtractor.extract_section", "name": "extract_section", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "section_name"], "arg_types": ["metadata_extractor.MetadataExtractor", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_section of MetadataExtractor", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_document_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "repository_id", "revision"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "metadata_extractor.MetadataExtractor.generate_document_id", "name": "generate_document_id", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "repository_id", "revision"], "arg_types": ["metadata_extractor.MetadataExtractor", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_document_id of MetadataExtractor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "metadata_extractor.MetadataExtractor.logger", "name": "logger", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ollama_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "metadata_extractor.MetadataExtractor.ollama_client", "name": "ollama_client", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "parse_date_with_fallbacks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "date_input", "filename_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "metadata_extractor.MetadataExtractor.parse_date_with_fallbacks", "name": "parse_date_with_fallbacks", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "date_input", "filename_date"], "arg_types": ["metadata_extractor.MetadataExtractor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_date_with_fallbacks of MetadataExtractor", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "metadata_extractor.MetadataExtractor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "metadata_extractor.MetadataExtractor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "metadata_extractor.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "metadata_extractor.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "metadata_extractor.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "metadata_extractor.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "metadata_extractor.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "metadata_extractor.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}}, "path": "C:\\home-repos\\reposense_ai\\metadata_extractor.py"}