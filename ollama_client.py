#!/usr/bin/env python3
"""
Ollama client for AI-powered content generation
Handles documentation and email content generation using Ollama API
"""

import logging
import requests
from typing import Tuple, Optional

from models import Config, CommitInfo


class OllamaClient:
    """Client for Ollama API interactions"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def call_ollama(self, prompt: str, system_prompt: str = "", model: Optional[str] = None) -> str:
        """Call Ollama API to generate content with optional model override"""
        try:
            # Use specified model or fall back to default
            selected_model = model or self.config.ollama_model

            # Check if model is available first
            if not self.is_model_available(selected_model):
                available_models = self.get_available_models()
                if available_models:
                    self.logger.warning(f"Model '{selected_model}' not available. Available models: {available_models}")
                    return f"Error: Model '{selected_model}' not available on server. Available models: {', '.join(available_models)}"
                else:
                    self.logger.error(f"No models available on Ollama server {self.config.ollama_host}")
                    return f"Error: No models available on Ollama server {self.config.ollama_host}"

            url = f"{self.config.ollama_host}/api/generate"

            payload = {
                "model": selected_model,
                "prompt": prompt,
                "system": system_prompt,
                "stream": False
            }

            self.logger.debug(f"Calling Ollama API at {url} with model {selected_model}")

            # Adjust timeout based on model size and complexity
            # Large models (20B+) need more time, especially for complex prompts
            timeout = self._get_timeout_for_model(selected_model)
            self.logger.debug(f"Using timeout of {timeout} seconds for model {selected_model}")

            response = requests.post(url, json=payload, timeout=timeout)
            response.raise_for_status()
            
            result = response.json()
            return result.get("response", "")
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error calling Ollama API: {e}")
            return ""
        except Exception as e:
            self.logger.error(f"Unexpected error calling Ollama: {e}")
            return ""
    
    def test_connection(self, timeout: Optional[int] = None) -> bool:
        """Test connection to Ollama server (not model-specific)"""
        try:
            if timeout is None:
                timeout = getattr(self.config, 'ollama_timeout_connection', 30)

            url = f"{self.config.ollama_host}/api/tags"
            self.logger.debug(f"Testing Ollama connection to {url} with timeout {timeout}s")
            response = requests.get(url, timeout=timeout)
            success = response.status_code == 200
            self.logger.debug(f"Ollama connection test result: {success} (status: {response.status_code})")
            return success
        except Exception as e:
            self.logger.debug(f"Ollama connection test failed: {e}")
            return False

    def get_available_models(self, timeout: Optional[int] = None) -> list:
        """Get list of available models from Ollama server"""
        try:
            if timeout is None:
                timeout = getattr(self.config, 'ollama_timeout_connection', 30)

            url = f"{self.config.ollama_host}/api/tags"
            response = requests.get(url, timeout=timeout)
            response.raise_for_status()

            data = response.json()
            models = [model['name'] for model in data.get('models', [])]
            self.logger.debug(f"Available models: {models}")
            return models
        except Exception as e:
            self.logger.debug(f"Failed to get available models: {e}")
            return []

    def is_model_available(self, model_name: str | None = None) -> bool:
        """Check if a specific model is available"""
        if model_name is None:
            model_name = self.config.ollama_model

        available_models = self.get_available_models()
        return model_name in available_models

    def _get_timeout_for_model(self, model_name: str) -> int:
        """Get appropriate timeout based on model size and type"""
        # Get base timeout from config, with fallback
        base_timeout = getattr(self.config, 'ollama_timeout_base', 180)

        # Adjust based on model characteristics
        model_lower = model_name.lower()

        # Very large models (20B+)
        if any(size in model_lower for size in ['20b', '33b', '70b', '180b']):
            return 300  # 5 minutes

        # Large models (7B-13B)
        elif any(size in model_lower for size in ['7b', '8b', '12b', '13b', '14b']):
            return 240  # 4 minutes

        # Medium models (3B-6B)
        elif any(size in model_lower for size in ['3b', '4b', '6b']):
            return 180  # 3 minutes

        # Small models (1B-2B)
        elif any(size in model_lower for size in ['1b', '2b', '1.7b']):
            return 120  # 2 minutes

        # Very small models (under 1B)
        elif any(size in model_lower for size in ['135m', '350m', '500m']):
            return 60   # 1 minute

        # Special cases for known slow models
        if 'deepseek' in model_lower or 'codestral' in model_lower:
            return base_timeout + 60  # Add extra time for complex models

        # Default for unknown models
        return base_timeout


    
    def generate_documentation(self, commit: CommitInfo) -> str:
        """Generate documentation for a commit using Ollama"""
        system_prompt = """You are a technical documentation generator and code review advisor. Your task is to create clear,
        comprehensive documentation based on code changes and provide development process feedback. Focus on:
        1. What changes were made
        2. Why the changes were made (based on commit message)
        3. Impact on the codebase
        4. Any important technical details
        5. Code review recommendations
        6. Documentation impact assessment

        Write in markdown format with appropriate headers and formatting."""
        
        prompt = f"""
        Generate comprehensive documentation and development process feedback for the following commit:

        Revision: {commit.revision}
        Author: {commit.author}
        Date: {commit.date}
        Message: {commit.message}

        Changed files:
        {chr(10).join(commit.changed_paths)}

        Diff:
        {commit.diff}

        Please provide your analysis in the following format:

        ## Summary
        [Brief summary of changes]

        ## Technical Details
        [Detailed technical analysis]

        ## Impact Assessment
        [Impact on codebase, users, and system functionality]

        ## Code Review Recommendation
        [Should this commit be code reviewed? Why or why not? Consider factors like:
        - Complexity of changes
        - Risk level (high/medium/low)
        - Areas affected (UI, backend, configuration, etc.)
        - Potential for introducing bugs
        - Security implications]

        ## Documentation Impact
        [Does this commit affect documentation? Consider:
        - Are user-facing features changed?
        - Are APIs or interfaces modified?
        - Are configuration options added/changed?
        - Are deployment procedures affected?
        - Should README, setup guides, or other docs be updated?]

        ## Recommendations
        [Any additional recommendations for follow-up actions]
        """
        
        self.logger.info(f"Generating documentation for revision {commit.revision}")
        return self.call_ollama(prompt, system_prompt)
    
    def generate_email_content(self, commit: CommitInfo) -> Tuple[str, str]:
        """Generate email subject and body for a commit using Ollama"""
        system_prompt = """You are an email generator for code commit notifications. Create:
        1. A clear, concise subject line
        2. A professional email body that summarizes the changes
        
        The email should be informative but not overly technical. Focus on business impact 
        and key changes that stakeholders should know about."""
        
        prompt = f"""
        Generate an email notification for the following repository commit:
        
        Revision: {commit.revision}
        Author: {commit.author}
        Date: {commit.date}
        Message: {commit.message}
        
        Changed files:
        {chr(10).join(commit.changed_paths)}
        
        Diff (summary):
        {commit.diff[:2000]}{'...' if len(commit.diff) > 2000 else ''}
        
        Please generate:
        1. EMAIL SUBJECT: [subject line]
        2. EMAIL BODY: [email content]
        
        Keep the subject line under 60 characters and the email body professional and concise.
        """
        
        self.logger.info(f"Generating email content for revision {commit.revision}")
        response = self.call_ollama(prompt, system_prompt)
        
        # Parse response to extract subject and body
        lines = response.split('\n')
        subject = "Repository Commit Notification"
        body = response
        
        for line in lines:
            if line.strip().startswith("EMAIL SUBJECT:"):
                subject = line.replace("EMAIL SUBJECT:", "").strip()
                break
        
        # Find email body section
        body_start = response.find("EMAIL BODY:")
        if body_start != -1:
            body = response[body_start + len("EMAIL BODY:"):].strip()
        
        return subject, body
