#!/usr/bin/env python3
"""
Abstract base class for repository backend plugins
Defines the interface that all repository backends (SVN, Git, etc.) must implement
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Iterator
from dataclasses import dataclass
from datetime import datetime
from models import CommitInfo, RepositoryConfig


@dataclass
class RepositoryInfo:
    """Information about a discovered repository"""
    name: str
    url: str
    path: str
    last_revision: Optional[str] = None
    last_author: Optional[str] = None
    last_date: Optional[str] = None
    size: Optional[int] = None
    repository_type: str = ""  # 'svn', 'git', etc.


class RepositoryBackend(ABC):
    """Abstract base class for repository backends"""
    
    def __init__(self, config):
        self.config = config
        self.logger = None  # Will be set by subclasses
    
    @property
    @abstractmethod
    def backend_type(self) -> str:
        """Return the type of this backend (e.g., 'svn', 'git')"""
        pass
    
    @abstractmethod
    def get_latest_revision(self, repo: RepositoryConfig) -> Optional[str]:
        """
        Get the latest revision/commit identifier from the repository
        
        Args:
            repo: Repository configuration
            
        Returns:
            Latest revision identifier (string to support different formats)
            None if error occurred
        """
        pass
    
    @abstractmethod
    def get_commit_info(self, repo: RepositoryConfig, revision: str) -> Optional[CommitInfo]:
        """
        Get detailed information about a specific commit
        
        Args:
            repo: Repository configuration
            revision: Revision/commit identifier
            
        Returns:
            CommitInfo object with commit details, None if error occurred
        """
        pass
    
    @abstractmethod
    def get_diff(self, repo: RepositoryConfig, revision: str) -> Optional[str]:
        """
        Get the diff/changes for a specific commit
        
        Args:
            repo: Repository configuration
            revision: Revision/commit identifier
            
        Returns:
            Diff content as string, None if error occurred
        """
        pass
    
    @abstractmethod
    def test_connection(self, repo: RepositoryConfig) -> bool:
        """
        Test if the repository is accessible with the given configuration
        
        Args:
            repo: Repository configuration
            
        Returns:
            True if connection successful, False otherwise
        """
        pass
    
    @abstractmethod
    def discover_repositories(self, base_url: str, username: Optional[str] = None, 
                            password: Optional[str] = None, max_depth: int = 3) -> List[RepositoryInfo]:
        """
        Discover repositories from a server/base URL
        
        Args:
            base_url: Base server URL to search
            username: Optional username for authentication
            password: Optional password for authentication
            max_depth: Maximum depth to search
            
        Returns:
            List of discovered repositories
        """
        pass
    
    @abstractmethod
    def get_repository_info(self, repo: RepositoryConfig) -> Dict[str, Any]:
        """
        Get detailed information about a repository

        Args:
            repo: Repository configuration

        Returns:
            Dictionary with repository information
        """
        pass

    @abstractmethod
    def browse_files(self, repo_url: str, username: Optional[str] = None,
                    password: Optional[str] = None, path: str = '/') -> List[Dict[str, Any]]:
        """
        Browse files and directories in the repository at the specified path

        Args:
            repo_url: Repository URL
            username: Optional username for authentication
            password: Optional password for authentication
            path: Path within the repository to browse (default: root)

        Returns:
            List of dictionaries containing file/directory information
            Each dict should have: name, type ('file' or 'directory'), size, modified
        """
        pass

    def validate_repository_config(self, repo: RepositoryConfig) -> List[str]:
        """
        Validate repository configuration for this backend
        
        Args:
            repo: Repository configuration to validate
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        if not repo.url:
            errors.append("Repository URL is required")
        
        if not repo.name:
            errors.append("Repository name is required")
        
        return errors
    
    def supports_authentication(self) -> bool:
        """
        Check if this backend supports username/password authentication
        
        Returns:
            True if authentication is supported
        """
        return True
    
    def supports_discovery(self) -> bool:
        """
        Check if this backend supports repository discovery
        
        Returns:
            True if discovery is supported
        """
        return True
    
    def get_command_base(self, repo: RepositoryConfig) -> List[str]:
        """
        Get base command with authentication for repository operations
        This is a helper method that backends can override

        Args:
            repo: Repository configuration

        Returns:
            List of command components
        """
        return []

    # Historical scanning methods

    def get_revision_range(self, repo: RepositoryConfig, start_revision: Optional[str] = None,
                          end_revision: Optional[str] = None) -> List[str]:
        """
        Get list of revisions in a range

        Args:
            repo: Repository configuration
            start_revision: Starting revision (inclusive), None for first revision
            end_revision: Ending revision (inclusive), None for latest revision

        Returns:
            List of revision identifiers in chronological order
        """
        # Default implementation - subclasses should override for efficiency
        revisions = []
        try:
            latest = self.get_latest_revision(repo)
            if not latest:
                return []

            # Simple implementation - get all revisions and filter
            # Subclasses should implement more efficient range queries
            start_num = int(start_revision) if start_revision else 1
            end_num = int(end_revision) if end_revision else int(latest)

            for rev in range(start_num, end_num + 1):
                revisions.append(str(rev))

        except (ValueError, TypeError):
            # Handle non-numeric revision systems (like Git hashes)
            pass

        return revisions

    def get_revisions_by_date_range(self, repo: RepositoryConfig, start_date: datetime,
                                   end_date: datetime) -> List[str]:
        """
        Get list of revisions within a date range

        Args:
            repo: Repository configuration
            start_date: Starting date (inclusive)
            end_date: Ending date (inclusive)

        Returns:
            List of revision identifiers in chronological order
        """
        # Default implementation - subclasses should override for efficiency
        revisions = []
        try:
            latest = self.get_latest_revision(repo)
            if not latest:
                return []

            # Simple implementation - check each revision's date
            # Subclasses should implement more efficient date queries
            for rev in range(1, int(latest) + 1):
                commit_info = self.get_commit_info(repo, str(rev))
                if commit_info and commit_info.date:
                    # Parse commit date and check if in range
                    try:
                        commit_date = datetime.fromisoformat(commit_info.date.replace('Z', '+00:00'))
                        if start_date <= commit_date <= end_date:
                            revisions.append(str(rev))
                    except (ValueError, TypeError):
                        continue

        except (ValueError, TypeError):
            pass

        return revisions

    def get_commit_batch(self, repo: RepositoryConfig, revisions: List[str]) -> Iterator[CommitInfo]:
        """
        Get commit information for multiple revisions efficiently

        Args:
            repo: Repository configuration
            revisions: List of revision identifiers

        Yields:
            CommitInfo objects for each valid revision
        """
        # Default implementation - subclasses can override for batch optimization
        for revision in revisions:
            commit_info = self.get_commit_info(repo, revision)
            if commit_info:
                yield commit_info

    def get_repository_statistics(self, repo: RepositoryConfig) -> Dict[str, Any]:
        """
        Get repository statistics for historical scanning planning

        Args:
            repo: Repository configuration

        Returns:
            Dictionary with statistics like total_revisions, first_revision, etc.
        """
        try:
            latest = self.get_latest_revision(repo)
            if not latest:
                return {}

            return {
                'latest_revision': latest,
                'total_revisions': int(latest) if latest.isdigit() else None,
                'first_revision': '1' if latest.isdigit() else None,
                'backend_type': self.backend_type
            }
        except Exception:
            return {}
