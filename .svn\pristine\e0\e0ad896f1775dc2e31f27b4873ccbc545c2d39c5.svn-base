# RepoSense AI Configuration Guide

## 🎯 Web Interface First Approach

RepoSense AI now uses a **web interface first** configuration system:

```
1. 🌐 Web Interface           (PRIMARY - Recommended)
2. 📄 Configuration File      (Automatic - Generated by web interface)
3. 🔧 Environment Variables   (OVERRIDE - Deployment only)
4. ⚙️ Application Defaults    (FALLBACK)
```

**The web interface is now the primary way to configure RepoSense AI.**

## 🚀 Quick Setup

### Step 1: Start RepoSense AI
```bash
# Start with default configuration (no environment setup needed!)
docker-compose up -d

# Access web interface
open http://localhost:5000
```

### Step 2: Configure via Web Interface
1. **Visit** http://localhost:5000
2. **Click** "Configuration" in the navigation
3. **Configure** your settings:
   - Ollama Host (e.g., `http://************:11434`)
   - Ollama Model (e.g., `qwen3` or `codeqwen:7b-chat-v1.5-q8_0`)
   - Email settings, repositories, etc.
4. **Click** "Save Configuration"

### Step 3: That's it! 🎉
Your configuration is automatically saved and will persist across restarts.

### For Production Deployment:
```bash
# 1. Update environment variables in docker-compose.yml
environment:
  - OLLAMA_BASE_URL=http://ollama:11434
  - OLLAMA_MODEL=codeqwen:7b-chat-v1.5-q8_0
  # Remove development-specific variables:
  # - FLASK_DEBUG=1
  # - REPOSENSE_AI_LOG_LEVEL=DEBUG

# 2. Deploy and check configuration
docker-compose up -d
docker exec reposense-ai python config_summary.py
```

## 📋 Key Configuration Values

| Setting | Environment Variable | Description | Example |
|---------|---------------------|-------------|---------|
| Ollama Host | `OLLAMA_BASE_URL` | Ollama server URL | `http://ollama:11434` |
| Ollama Model | `OLLAMA_MODEL` | AI model name | `codeqwen:7b-chat-v1.5-q8_0` |
| Web Host | `REPOSENSE_AI_WEB_HOST` | Web interface host | `0.0.0.0` |
| Web Port | `REPOSENSE_AI_WEB_PORT` | Web interface port | `5000` |

## 🔍 Troubleshooting

### Check Your Current Configuration:
```bash
python config_summary.py
```

This shows:
- ✅ Which config file is being used
- ✅ Which environment variables are active
- ✅ The final effective configuration
- ✅ Any issues found

### Common Issues:

**1. Web form changes don't stick**
- **Cause**: Environment variables override form saves
- **Solution**: Update environment variables instead of web form

**2. "ollama-server-local" appears in config**
- **Cause**: Old configuration values
- **Solution**: Set `OLLAMA_BASE_URL` environment variable

**3. Model not found errors**
- **Cause**: Wrong model name in configuration
- **Solution**: Check available models and update `OLLAMA_MODEL`

## 🛠️ Configuration Tools

### `config_summary.py`
Shows your effective configuration and validates settings.

### `fix_ollama_config.py`
Automatically fixes common Ollama configuration issues.

### `.env.template`
Template for environment variable configuration.

## 📝 Best Practices

1. **Use environment variables for deployment settings**
   - Ollama host/model
   - Web interface settings
   - Secrets and credentials

2. **Use config files for application settings**
   - Repository configurations
   - User management
   - Email settings

3. **Always check your effective configuration**
   ```bash
   python config_summary.py
   ```

4. **For Docker deployments, prefer environment variables**
   ```yaml
   environment:
     - OLLAMA_BASE_URL=http://ollama:11434
     - OLLAMA_MODEL=codeqwen:7b-chat-v1.5-q8_0
   ```

## 🎯 Examples

### Development (docker-compose.yml):
```yaml
environment:
  # Development overrides
  - REPOSENSE_AI_ENV=development
  - FLASK_DEBUG=1
  - REPOSENSE_AI_LOG_LEVEL=DEBUG
  # Ollama configuration
  - OLLAMA_BASE_URL=http://your-ollama-server:11434
  - OLLAMA_MODEL=your-preferred-model
  # Web interface
  - REPOSENSE_AI_WEB_HOST=0.0.0.0
  - REPOSENSE_AI_WEB_PORT=5000
```

### Production (docker-compose.yml):
```yaml
environment:
  # Production settings
  - REPOSENSE_AI_ENV=production
  - REPOSENSE_AI_LOG_LEVEL=INFO
  # Ollama configuration
  - OLLAMA_BASE_URL=http://ollama:11434
  - OLLAMA_MODEL=your-production-model
  # Web interface
  - REPOSENSE_AI_WEB_HOST=0.0.0.0
  - REPOSENSE_AI_WEB_PORT=5000
```

### Config File (config.json):
```json
{
  "ollama_host": "http://ollama:11434",
  "ollama_model": "your-model-name",
  "repositories": [...],
  "users": [...]
}
```

**Remember**: Environment variables will override the config file values!
