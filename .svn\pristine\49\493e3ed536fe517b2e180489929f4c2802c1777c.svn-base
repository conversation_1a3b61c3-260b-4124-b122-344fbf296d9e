# Metadata Extraction Service

## Overview

The `MetadataExtractor` service is a centralized component that consolidates all metadata extraction logic across RepoSense AI. This service was introduced to eliminate code duplication and ensure consistent metadata processing across all components.

## Architecture

### Centralized Design

Previously, metadata extraction logic was duplicated across multiple services:
- `HistoricalScanner` - ~200 lines of extraction code
- `DocumentProcessor` - ~180 lines of extraction code  
- `DocumentService` - ~150 lines of extraction code

**Total eliminated duplication**: ~500+ lines of code

The new `MetadataExtractor` service provides a single source of truth for all metadata operations.

### Hybrid Approach

The service uses a hybrid extraction strategy:

1. **Fast Heuristic Extraction** (Primary)
   - Pattern-based text analysis
   - Section-based content parsing
   - Keyword detection algorithms
   - Performance optimized for speed

2. **LLM Fallback** (Secondary)
   - Used when heuristics fail
   - Comprehensive AI analysis
   - JSON-structured responses
   - Handles edge cases

## Core Features

### Metadata Types Extracted

#### Code Review Analysis
- **Recommendation**: Boolean (required/not required)
- **Priority**: HIGH/MEDIUM/LOW classification
- **Rationale**: Extracted from AI analysis sections

#### Risk Assessment
- **Level**: HIGH/MEDIUM/LOW classification
- **Indicators**: Breaking changes, major impacts, critical sections
- **Context**: Multi-section analysis for comprehensive assessment

#### Documentation Impact
- **Required**: Boolean (updates needed/not needed)
- **Scope**: Identifies affected documentation areas
- **Priority**: Integration with code review recommendations

#### AI Summary Extraction
- **Commit Messages**: Generates meaningful commit messages from AI summaries
- **Content Processing**: Extracts concise summaries from documentation
- **Fallback Logic**: Multiple extraction strategies for reliability

### Utility Functions

#### Date Parsing
```python
def parse_date_with_fallbacks(self, date_input: Any, filename_date: Optional[str] = None) -> datetime:
    # Repository date (primary)
    # Filename date (secondary) 
    # Current time (fallback)
```

#### Document ID Generation
```python
def generate_document_id(self, repository_id: str, revision: int) -> str:
    return f"{repository_id}_{revision}"
```

#### Section Extraction
```python
def extract_section(self, content: str, section_name: str) -> Optional[str]:
    # Markdown section parsing with regex
    # Case-insensitive matching
    # Multi-line content support
```

## Integration Points

### HistoricalScanner Integration

```python
# Before (duplicated logic)
metadata = self._extract_document_metadata(documentation)
ai_summary = self._extract_ai_summary_from_documentation(documentation)
doc_date = self._parse_date_manually(commit_info.date)

# After (centralized service)
metadata = self.metadata_extractor.extract_all_metadata(documentation)
ai_summary = self.metadata_extractor.extract_ai_summary(documentation)
doc_date = self.metadata_extractor.parse_date_with_fallbacks(commit_info.date)
```

### DocumentProcessor Integration

```python
# Before (individual method calls)
code_review_recommended = self._extract_code_review_recommendation(content)
code_review_priority = self._extract_code_review_priority(content)
documentation_impact = self._extract_documentation_impact(content)
risk_level = self._extract_risk_level(content)

# After (batch extraction)
metadata = self.metadata_extractor.extract_all_metadata(content)
code_review_recommended = metadata.get('code_review_recommended')
code_review_priority = metadata.get('code_review_priority')
documentation_impact = metadata.get('documentation_impact')
risk_level = metadata.get('risk_level')
```

## Performance Optimizations

### Heuristic-First Strategy
- Fast pattern matching for common cases
- Reduces LLM calls by ~80%
- Sub-second processing for most documents

### Batch Processing
- Single method call for all metadata types
- Reduced parsing overhead
- Consistent field validation

### Caching Strategy
- Section extraction results cached
- Reduced redundant parsing
- Memory-efficient implementation

## Error Handling

### Graceful Degradation
```python
try:
    # Heuristic extraction
    result = self._extract_heuristic(content)
    if result is not None:
        return result
except Exception:
    pass

# LLM fallback
if self.ollama_client:
    return self._extract_with_llm(content)

# Final fallback
return None
```

### Logging Strategy
- Debug-level logging for extraction attempts
- Warning-level for fallback usage
- Error-level for complete failures

## Benefits Achieved

### Code Quality
- **Eliminated Duplication**: ~500+ lines of duplicated code removed
- **Single Source of Truth**: All extraction logic centralized
- **Consistent Results**: Identical behavior across all services
- **Better Testability**: Centralized logic easier to unit test

### Maintenance
- **Single Update Point**: Changes only need to be made in one place
- **Reduced Complexity**: Simplified service dependencies
- **Better Documentation**: Centralized documentation for all extraction logic

### Performance
- **Optimized Algorithms**: Shared optimizations benefit all services
- **Reduced Memory Usage**: Eliminated duplicate code paths
- **Faster Processing**: Optimized heuristic algorithms

### Reliability
- **Consistent Behavior**: Same extraction logic everywhere
- **Better Error Handling**: Centralized error handling strategies
- **Improved Fallbacks**: Comprehensive fallback mechanisms

## Future Enhancements

### Planned Improvements
- **Machine Learning Integration**: Train models on extraction patterns
- **Custom Extraction Rules**: User-configurable extraction patterns
- **Performance Monitoring**: Extraction time and accuracy metrics
- **Advanced Caching**: Persistent caching for repeated extractions

### Extensibility
- **Plugin Architecture**: Support for custom extraction plugins
- **API Integration**: External metadata source integration
- **Batch Processing**: Large-scale document processing capabilities
