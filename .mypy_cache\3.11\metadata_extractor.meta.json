{"data_mtime": 1754867751, "dep_lines": [8, 9, 10, 11, 12, 13, 16, 269, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 25, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["logging", "re", "json", "datetime", "typing", "pathlib", "document_database", "prompt_templates", "builtins", "_frozen_importlib", "abc", "enum", "json.decoder", "models", "types", "typing_extensions"], "hash": "03ccb7f9e59e3370a675fdf3c23cc4c1fd2a5424", "id": "metadata_extractor", "ignore_all": false, "interface_hash": "cd8c5feb7e6c846245d6c379577aae244c58a378", "mtime": 1754867763, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\metadata_extractor.py", "plugin_data": null, "size": 16502, "suppressed": [], "version_id": "1.15.0"}