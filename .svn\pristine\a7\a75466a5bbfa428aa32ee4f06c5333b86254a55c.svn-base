# RepoSense AI - Unified Docker Setup

## 🎯 Simplified Docker Architecture

RepoSense AI now uses a **truly unified Docker approach** with just two files:

### 📁 Docker Files Structure

```
├── Dockerfile          # ✅ Unified Dockerfile
├── docker-compose.yml  # ✅ Single compose file (works for everything)
├── .env.example        # ✅ Optional environment overrides
└── .dockerignore       # ✅ Optimized build context
```

**That's it! Just 2 essential files instead of 5+ Docker files.**

## 🚀 Quick Start

### For Everyone (Development & Production)
```bash
# That's it! Works for both development and production
docker-compose up -d

# Access web interface
open http://localhost:5000
```

### Optional: Development Mode
```bash
# Copy example environment file
cp .env.example .env

# Edit .env and uncomment development settings:
# REPOSENSE_AI_LOG_LEVEL=DEBUG
# REPOSENSE_AI_DB_DEBUG=true
# FLASK_DEBUG=1

# Restart to apply
docker-compose up -d
```

## 🔧 Configuration Approach

### ✅ **NEW: Web Interface First**
- **Primary**: Configure via web interface at http://localhost:5000
- **Automatic**: Settings saved to `data/config.json`
- **Persistent**: Configuration survives container restarts
- **No Environment Variables**: Minimal dependency on env vars

### 🔧 **Environment Variables (Optional)**
Only used for deployment overrides:
```bash
# Optional deployment overrides
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen3
REPOSENSE_AI_WEB_HOST=0.0.0.0
REPOSENSE_AI_WEB_PORT=5000
```

## 📋 Files Explained

### `docker-compose.yml` (The Only One!)
- **Purpose**: Works for both development and production
- **Build**: Uses unified Dockerfile
- **Volumes**: Essential data and logs only
- **Environment**: Minimal (web host/port only)
- **Customization**: Via optional `.env` file

### `.env.example` (Optional Template)
- **Purpose**: Shows available environment overrides
- **Usage**: Copy to `.env` and customize if needed
- **Development**: Uncomment debug settings
- **Production**: Uncomment deployment overrides

## 🏗️ Unified Dockerfile Features

### Smart Build Arguments
```dockerfile
ARG MODE=development  # Controls dev vs prod behavior
```

### Conditional Dependencies
- **Development**: Includes debugging tools (git, vim, nano, htop)
- **Production**: Minimal dependencies for security

### Automatic Mode Detection
- **Development**: `FLASK_DEBUG=1`, hot reload enabled
- **Production**: `FLASK_DEBUG=0`, optimized for performance

## 🎯 Benefits of Unified Approach

### ✅ **Simplified Maintenance**
- **Single Dockerfile** to maintain instead of 5
- **Consistent** build process across environments
- **Better caching** with shared layers

### ✅ **Improved Developer Experience**
- **No environment setup** required for basic usage
- **Web interface** for all configuration
- **Hot reload** in development mode
- **Production parity** with same base image

### ✅ **Better Configuration Management**
- **Web interface first** - no more environment variable confusion
- **Persistent settings** in config.json
- **Clear separation** between deployment and user configuration

## 🔄 Migration from Old Setup

### If you have existing environment variables:
```bash
# Run migration script to convert to config file
python migrate_to_config.py

# Or manually configure via web interface
open http://localhost:5000
```

### If you have old Docker files:
```bash
# Clean up old files (already done if you used migrate_docker.py)
rm -rf docker_backup/
```

## 🛠️ Advanced Usage

### Custom Build
```bash
# Build specific mode
docker build --build-arg MODE=development -t reposense-ai:dev .
docker build --build-arg MODE=production -t reposense-ai:prod .
```

### Health Checks
```bash
# Check container health
docker-compose ps
docker-compose logs reposense-ai
```

### Configuration Summary
```bash
# View current configuration
docker-compose exec reposense-ai python config_summary.py
```

## 🎉 Result

**Before**: 5 Dockerfiles + 3 docker-compose files + complex environment setup
**After**: 1 Dockerfile + 1 docker-compose.yml + web interface configuration

**Setup time**: From 15+ minutes to 30 seconds
**Configuration**: From environment variables to web interface
**Maintenance**: From 8+ files to 2 files
**User experience**: `docker-compose up -d` and you're done!

The truly unified approach makes RepoSense AI incredibly simple to deploy, configure, and maintain! 🚀
